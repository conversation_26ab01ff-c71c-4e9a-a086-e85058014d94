{"name": "@yqz/nest", "version": "0.0.333", "description": "Common module and utility building blocks for badass NestJS applications", "license": "MIT", "keywords": ["NestJS"], "main": "lib/index.js", "typings": "lib/index.d.ts", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "repository": {"type": "git"}, "scripts": {"build": "tsc --build tsconfig.build.json", "build:watch": "tsc --build tsconfig.build.json --watch", "dev": "tsc-watch -p tsconfig.build.json --onSuccess \"nodemon\"", "test": "jest"}, "publishConfig": {"access": "public"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.ts$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}, "dependencies": {"@opentelemetry/auto-instrumentations-node": "^0.40.0", "@opentelemetry/core": "^1.15.0", "@opentelemetry/exporter-jaeger": "^1.15.0", "@opentelemetry/exporter-trace-otlp-proto": "^0.40.0", "@opentelemetry/propagator-jaeger": "^1.15.0", "@opentelemetry/resources": "^1.15.0", "@opentelemetry/sdk-node": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.15.0", "axios": "1.1.2", "redis": "^3.1.2", "shelljs": "^0.8.5", "uuid": "^9.0.0"}, "peerDependencies": {"@golevelup/nestjs-rabbitmq": "3.3.0", "@nestjs/common": "^9.0.5", "@nestjs/core": "^9.0.5", "@nestjs/platform-express": "^9.0.5", "@nestjs/schedule": "^2.1.0", "@nestjs/swagger": "^6.1.3", "reflect-metadata": "^0.1.13", "rxjs": "^7.5.6", "typeorm": "^0.3.10"}, "devDependencies": {"@types/redis": "^2.8.32"}, "gitHead": "4d9b7fb54f6b0b3d8ea9ad2399801c421ee8aa7b"}