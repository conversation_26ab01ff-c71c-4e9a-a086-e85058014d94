import { AxiosRequestConfig } from 'axios';
import { Message } from '../types';
import { ApiClient } from './api-client';
import { ManagerMessageDTO } from '../types/message.type';

const config: AxiosRequestConfig = {
  baseURL: process.env.YQZ_MESSAGE_SERVICE_API,
};

export const MsgApi = {
  ping,
  sendMessage,
  sendJpushMessageCredit,
  searchMessageList,
};

async function ping() {
  const res = await ApiClient.get('/', config);
  return res.data;
}

async function sendMessage(message: Message.Message) {
  const res = await ApiClient.post('/msg/send', message, config);
  return res.data;
}

/**
 * 推送app内横幅
 * @param params 
 * @returns 
 */
async function sendJpushMessageCredit(params: {
  channel: string,
  from: string,
  to: string[],
  payload: any
}) {
  const res = await ApiClient.post('jpush/unicast/send/credit', params, config);
  return res.data.data;
} 

/**
 * 推送消息列表
 * @param params 
 * @returns 
 */
async function searchMessageList(params: ManagerMessageDTO) {
  const res = await ApiClient.post('/timon/person-message/list', params, config);
  return res.data;
}
