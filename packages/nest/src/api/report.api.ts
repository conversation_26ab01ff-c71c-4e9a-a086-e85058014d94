import { AxiosRequestConfig } from 'axios';
import { ApiClient } from './api-client';

const config: AxiosRequestConfig = {
  baseURL: process.env.YQZ_REPORT_SERVICE_API,
};

export const ReportApi = {
  projectCorpReport,
  projectSignInStatistics
};

/**
 * 根据公司id和日期查询工地概况
 * @param data
 * @returns
 */
async function projectCorpReport(data: {
  deptId: string;
  date: string;
  dayType: 'day' | 'week' | 'month';
}) {
  const res = await ApiClient.post('/corp/report/project', data, config);
  return res.data.data;
}

/**
 * 查询工地签到统计
 * @param data 
 * @returns 
 */
async function projectSignInStatistics(data: {
  companyId: string;
  projectId: string;
}): Promise<{ total: number, todayCount: number }> {
  const res = await ApiClient.post('/project/signin/statistics', data, config);
  return res?.data?.data || null;
}
