import { ApiClient } from './api-client';
import { YqzException } from '../nestjs';
import * as _ from 'lodash';
import FormData = require('form-data');
import request = require('request');
import { Media } from '../types';
import { AxiosRequestConfig } from 'axios';
import { ImageFrameTag } from '../types/media.type';

export const MediaApi = {
  uploadMediaByUrl,
  getMediasDetail,
  imageFrameTag,
  uploadMediaByType,
};

const config: AxiosRequestConfig = {
  baseURL: process.env.YQZ_MEDIA_API,
};

/**
 * 根据url上传oss
 * @param fileName 文件名
 * @param ossUrl 媒体url
 * @param mediaType 媒体类型
 * @returns
 */
async function uploadMediaByUrl({
  fileName,
  ossUrl,
  mediaType,
  creationDate,
}: {
  fileName: string;
  ossUrl: string;
  mediaType: string;
  creationDate?: string;
}): Promise<Media.UploadResponse> {
  try {
    const data = new FormData();
    //直接传buffer，实现文件不落地
    const buffer = await request.get(ossUrl);
    data.append('file', buffer, {
      contentType: Media.MediaType[mediaType],
      filename: fileName,
    });
    data.append('mediaType', mediaType ? mediaType : Media.YqzMediaType.IMAGE);
    if (creationDate) data.append('creationDate', creationDate);
    const res = await ApiClient.post('/file/upload', data, {
      baseURL: config.baseURL,
      headers: {
        ...data.getHeaders(),
      },
    });
    return res.data;
  } catch (e) {
    console.log(`uploadPhoto err:${JSON.stringify(e)}`);
    throw new YqzException('文件上传失败');
  }
}

/**
 * 根据类型上传oss
 * @param fileName 文件名
 * @param type url或buffer
 * @param media 传递实体
 * @param mediaType 媒体类型
 * @returns
 */
async function uploadMediaByType({
  fileName,
  type,
  media,
  mediaType,
  creationDate,
}: {
  fileName: string;
  type: 'url' | 'buffer';
  media: {
    url?: string;
    buffer?: Buffer;
  };
  mediaType: string;
  creationDate?: string;
}): Promise<Media.UploadResponse> {
  try {
    const data = new FormData();
    //直接传buffer，实现文件不落地
    const buffer =
      type === 'url' ? await request.get(media?.url) : media?.buffer;
    data.append('file', buffer, {
      contentType: Media.MediaType[mediaType],
      filename: fileName,
    });
    data.append('mediaType', mediaType ? mediaType : Media.YqzMediaType.IMAGE);
    if (creationDate) data.append('creationDate', creationDate);
    const res = await ApiClient.post('/file/upload', data, {
      baseURL: config.baseURL,
      headers: {
        ...data.getHeaders(),
      },
    });
    return res.data;
  } catch (e) {
    console.log(`uploadPhoto err:${JSON.stringify(e)}`);
    throw new YqzException('文件上传失败');
  }
}

/**
 * 查询媒体信息
 * @param mediaIds 媒体id集合
 * @returns
 */
async function getMediasDetail(mediaIds: string[]) {
  const res = await ApiClient.post(
    '/file/photosDetail',
    {
      photoIds: mediaIds,
    },
    config
  );
  return res.data;
}

/**
 * 图片绘制矩形框标记
 * @param data
 * @returns
 */
async function imageFrameTag(
  data: ImageFrameTag
): Promise<{ ossFileName: string; mediaUri: string }> {
  const res = await ApiClient.post('/image/frame/tag', data, config);
  return res.data || null;
}
