import { AxiosRequestConfig } from 'axios';
import { ApiClient } from './api-client';

const config: AxiosRequestConfig = {
  baseURL: process.env.YQZ_MP_PLATFORM_API,
};

export const MpPlatformApi = {
  ping,
  getWeChatTemplate,
  getWxAppIdByCompanyId,
  getAccessToken,
  code2Session
};

async function ping() {
  const res = await ApiClient.get('/', config);
  return res.data;
}

async function getWeChatTemplate(appId: string, templateName: string) {
  const res = await ApiClient.post(
    '/wx/oa/template/detail',
    {
      appId: appId,
      templateName: templateName,
    },
    config
  );
  return res.data;
}

async function getWxAppIdByCompanyId(companyId: number) {
  const res = await ApiClient.post(
    '/company/app/get',
    {
      companyId: companyId,
    },
    config
  );
  return res.data;
}

async function getAccessToken(appId: string) {
  const res = await ApiClient.post('/wx/mp/token', {
    appId: appId,
  }, config);
  return res.data;
}

async function code2Session(data: {appId: string, code: string}) {
  const res = await ApiClient.post('/wx/code', data, config);
  return res.data;
}
