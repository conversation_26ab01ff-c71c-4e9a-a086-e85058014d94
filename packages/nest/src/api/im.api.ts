import { AxiosRequestConfig } from 'axios';
import { ApiBaseRes } from '../types/api.type';
import { ApiClient } from './api-client';

const config: AxiosRequestConfig = {
  baseURL: process.env.YQZ_IM_SERVICE_API,
};

export const ImApi = {
  searchGrpuMembers,
  getGroupByProjectId,
};

async function searchGrpuMembers(data: {
  jmGroupId: string;
}): Promise<ApiBaseRes<{ personId: string }[]>> {
  const res = await ApiClient.post('/im/group/member/search', data, config);
  return res.data;
}

async function getGroupByProjectId(data: {
  projectId: string;
}): Promise<{ count: number, jmGroupId: string }> {
  const res = await ApiClient.post('/im/web/group/project', data, config);
  return res?.data?.data;
}
