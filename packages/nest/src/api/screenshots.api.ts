import { AxiosRequestConfig } from 'axios';
import { ApiClient } from './api-client';

const config: AxiosRequestConfig = Object.freeze({
  baseURL: process.env.YQZ_SCREENSHOTS_SERVICE_API,
});

export const ScreenShotsApi = {
  ping,
  getCardQrScene,
};

async function ping() {
  const res = await ApiClient.get('/', config);
  return res.data;
}

async function getCardQrScene(params: any) {
  const screenConfig: AxiosRequestConfig = {
    baseURL: process.env.YQZ_SCREENSHOTS_SERVICE_API,
    params: params,
    responseType: 'arraybuffer', // 设置响应类型为 'stream' | 'arraybuffer'
  };
  const res = await ApiClient.get('/get/screenshots', screenConfig);
  return res.data;
}
