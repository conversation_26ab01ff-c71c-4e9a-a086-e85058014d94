import { AxiosRequestConfig } from 'axios';
import { Task } from '../types';
import { ApiClient } from './api-client';

const config: AxiosRequestConfig = {
  baseURL: process.env.YQZ_SMART_OPS_API,
};

export const SmartOpsApi = {
  ping,
  saveTask,
  reportTaskStatus,
};

async function ping() {
  const res = await ApiClient.get('/', config);
  return res.data;
}

async function saveTask(task: Task.Task) {
  const res = await ApiClient.post('/task', task, config);
  return res.data;
}

async function reportTaskStatus(status: Task.Log) {
  let res: any = null;
  try {
    res = await ApiClient.post('/task/status', status, config);
  } catch (error) {
    console.error(`Failed to report task status: ${error} status: ${JSON.stringify(status)}`);
  }
  return res?.data;
}
