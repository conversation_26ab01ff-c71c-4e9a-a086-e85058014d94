import { AxiosRequestConfig } from 'axios';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Manager,
  Person,
  Project,
  Department,
  Company,
} from '../types';
import {
  Analytics,
  MemberRole,
  ProblemLogUpdate,
  ProblemUpdate,
  ProjectCameraUpdateReq,
  ProjectProgressFilterReq,
  ProjectProgressInfoRes,
  ProjectProgressStageChangeReq,
  SaveAppCameraResultReq,
  SaveTagReqVO,
  StageCharacteristicByProjectIdRes,
} from '../types/project.type';
import { ApiClient } from './api-client';
import { HandleApproveByApiVO, ReportUpdateVO, SaveDraftNoCriteriaVO } from '../types/acceptance.type';
import { YqzMedia } from '../types/media.type';

const config: AxiosRequestConfig = Object.freeze({
  baseURL: process.env.YQZ_AGILE_SERVICE_API,
});

export const AgileApi = {
  ping,
  jwtSign,
  generateManagerToken,
  getUserScopeProjects,
  getUserScopeManagers,
  getUserScopeDepts,
  searchSevenDayActionProblemList,
  searchProjectCameraSet,
  updateProjectCameraSet,
  // person
  getPersonDetail,
  searchPerson,
  getManagerDetail,
  getOrgFaceSearch,
  getUserConfig,
  // project
  selectProjectIdsIncludeDelete,
  searchProject,
  searchProjectList,
  getScopeProjectList,
  projectBelongDept,
  projectProgressFilter,
  projectProgressInfo,
  projectProgressStageChange,
  searchProjectInfo,
  getFirstDefaultEvent,
  changeConstructionStatus,
  // project member
  searchProjectMemberInfo,
  // sign
  searchSignInUrlsList,
  saveProjectSignInAppCameraResult,
  // problem
  searchCompanyProblemSet,
  saveProjectProblem,
  deleteProjectProblem,
  updateProjectProblem,
  updateProblemSolved,
  saveLogsByProblemUpdate,
  // manager
  searchManager,
  updateManager,
  //department
  searchSubDepartment,
  getRootDept,
  intraSearchHierarchy,
  searchDeptInfo,
  searchDeptListInfo,
  searchDeviceDeptName,
  screenDeviceListByDeptId,
  getCompanyPushSetList,
  getProjectById,
  getPushPersonIdsByCompanySet,
  JMsgPush,
  managerSearch,
  searchCurrentDeptMapByManager,
  searchFirstDeptByManager,
  searchDepartmentListByCompanyId,
  searchSubDeptIdListByDeptIdList,
  orgHirachySearch,
  // analytics
  bodyParse,
  managerList,
  findProjectSignIn,
  searchMediaAnnotationList,
  // tag
  searchCompanyTagsForApi,
  searchCompanyTagsForApiV2,
  createOrUpdateCompanyTag,
  getCustomCompanyInfo,
  searchMatterSet,
  isDepartmentHeadAndSuperiorByProject,
  // node
  getProjectNodes,
  searchProjectNodePlanList,
  updateProjectNodePlan,
  deleteProjectNodePlan,
  searchProjectNodePlanLogList,
  // stage
  getStageCharacteristicByProjectId,
  getProjectProblemListStatus,
  getProjectProblemStatus,
  // acceptance
  saveDraftNoCriteria,
  deleteOtherSameReportDraft,
  getAcceptanceReportDetail,
  modifyReportByApi,
  approvalExecuteByApi,
  syncDraftStageMedia,
  syncMemberAcceptanceReportDraft
};

async function ping() {
  const res = await ApiClient.get('/', config);
  return res.data;
}

/**
 * 根据权限获取可视范围工地
 * @param
 * @returns
 */
async function getUserScopeProjects(
  managerId: string
): Promise<{ projectIdList: any[] }> {
  const res = await ApiClient.post(
    '/project/user-scope/search',
    { managerId },
    {
      ...config,
    }
  );
  return res.data;
}

/**
 * 根据权限获取可视范围部门成员
 * @param
 * @returns
 */
async function getUserScopeManagers(
  managerId: string
): Promise<{ managerId: string; personId: string }[]> {
  const res = await ApiClient.post(
    '/org/member/subordinate/search',
    {
      managerId,
    },
    { ...config }
  );
  return res.data.data;
}

/**
 * 根据权限获取可视范围部门
 * @param
 * @returns
 */
async function getUserScopeDepts(
  managerId: string
): Promise<{ departmentId: string; departmentName: string }[]> {
  const res = await ApiClient.post(
    '/org/member/subordinate/dept/search',
    {
      managerId,
    },
    { ...config }
  );
  return res.data.data;
}

/**
 * 搜索person
 * @param params
 */
async function searchPerson(params: Person.SearchReq) {
  const res = await ApiClient.post<ApiBaseRes<Person.SearchRes>>(
    '/person/search',
    params,
    config
  );
  return res.data;
}

/**
 * 搜索person
 * @param params
 */
async function getPersonDetail(params: Person.GetReq) {
  const res = await ApiClient.post<ApiBaseRes<Person.Dto>>(
    '/person/detail/get',
    params,
    config
  );
  return res.data;
}

async function selectProjectIdsIncludeDelete(param: any) {
  const res = await ApiClient.post('/project/search/bind/list', param, config);
  return res.data;
}

async function searchProject(
  param: Project.SearchReq
): Promise<ApiBaseRes<Project.SearchRes[]>> {
  const res = await ApiClient.post('/project/search/v2', param, config);
  return res.data;
}

/**
 * 获取manager detail
 */
async function getManagerDetail(param: Manager.GetReq) {
  const res = await ApiClient.post<Manager.Dto>(
    '/member/detail',
    param,
    config
  );
  return res.data;
}

async function searchManager(param: Manager.SearchReq) {
  const res = await ApiClient.post<Manager.Dto[]>(
    '/member/search',
    param,
    config
  );
  return res.data;
}

/**
 * 修改manager信息
 * @param param
 * @returns
 */
async function updateManager(
  param: Manager.UpdateReq
): Promise<Manager.UpdateRes> {
  const res = await ApiClient.post('/member/update', param, config);
  return res.data?.data;
}

async function searchSevenDayActionProblemList(params: {
  companyId: string;
  projectIdList?: string[];
  sevenDayActionProblem?: string;
}) {
  const res = await ApiClient.post(
    '/live/marketing/problem/info/list',
    params,
    config
  );
  return res.data.data;
}

/**
 * 获取子部门列表
 * @param param
 * @returns
 */
async function searchSubDepartment(param: {
  departmentId: string;
  includingSelf?: boolean;
}) {
  const res = await ApiClient.post('/org/department/search/sub', param, config);
  return res.data.data;
}

/**
 * 查询根部门接口,api调用
 * @param param
 * @returns
 */
async function getRootDept(param: { companyId: string }) {
  const res = await ApiClient.post('/dept/root/search', param, config);
  return res.data;
}

/**
 * 查询工地业主权限配置接口,api调用
 * @param param
 * @returns
 */
async function searchProjectCameraSet(param: {
  projectId: string;
  role: string;
}) {
  const res = await ApiClient.post(
    '/permissions/project/camera/search',
    param,
    config
  );
  return res.data.data;
}

/**
 * 修改工地业主权限配置接口,api调用
 * @param param
 * @returns
 */
async function updateProjectCameraSet(params: ProjectCameraUpdateReq) {
  const res = await ApiClient.post(
    '/permissions/project/camera/update',
    params,
    config
  );
  return res.data.data;
}

/**
 * 搜索组织结构层级（内部调用）
 * @param param
 * @returns
 */
async function intraSearchHierarchy(param: {
  companyId: string;
  departmentId?: string;
}) {
  const res = await ApiClient.post(
    '/org/hierarchy/search/intra',
    param,
    config
  );
  return res.data?.data;
}

/**
 * 根据部门id查询部门信息
 * @param param
 * @returns
 */
async function searchDeptInfo(param: { departmentId: string }) {
  const res = await ApiClient.post('/dept/info', param, config);
  return res.data?.data;
}

/**
 * 根据部门id查询部门信息.
 * @param param
 * @returns
 */
async function searchDeptListInfo(param: {
  departmentIds: string[];
  companyId?: string;
  name?: string;
}) {
  const res = await ApiClient.post('/dept/list/info', param, config);
  return res.data?.data;
}

/**
 * 查询设备归属部门名
 * @param param
 * @returns
 */
async function searchDeviceDeptName(param: {
  list: Department.SearchDeviceDeptName[];
}) {
  const res = await ApiClient.post('/dept/name/device', param, config);
  return res.data?.data;
}

/**
 * 查询设备归属部门名
 * @param param
 * @returns
 */
async function screenDeviceListByDeptId(param: {
  departmentId: string;
  list: Department.SearchDeviceDeptName[];
}) {
  const res = await ApiClient.post('/dept/screen/device', param, config);
  return res.data?.data;
}

/**
 * 查询公司问题配置
 * @param param
 * @returns
 */
async function searchCompanyProblemSet(param: { companyId: string }) {
  const res = await ApiClient.post(
    '/project/problem/set/search/notoken',
    param,
    config
  );
  return res.data?.data;
}

/**
 * 保存工地问题
 * @param param
 * @returns
 */
async function saveProjectProblem(
  param: Project.ProjectProblemSaveDto
): Promise<string[]> {
  const res = await ApiClient.post('/project/problem/save', param, config);
  return res.data?.data;
}

/**
 * 删除工地问题
 * @param param 
 * @returns 
 */
async function deleteProjectProblem(
  param: Project.DeleteDto
) {
  const res = await ApiClient.post('/project/problem/delete', param, config);
  return res.data?.data;
}

/**
 * 修改工地问题
 * @param param
 * @returns
 */
async function updateProjectProblem(
  param: Project.ProjectProblemUpdateDto
): Promise<string[]> {
  const res = await ApiClient.post('/project/problem/update', param, config);
  return res.data?.data;
}

/**
 * 修改工地问题
 * @param param
 * @returns
 */
async function getCompanyPushSetList(param: Company.companyPushSetList) {
  const res = await ApiClient.post('/company/pushset/yqzList', param, config);
  return res.data?.data;
}

async function getProjectById(param: Project.DetailById) {
  const res = await ApiClient.post('/project/yqzDetail', param, config);
  return res.data?.data;
}

async function searchSignInUrlsList(param: { signInIds: string[] }) {
  const res = await ApiClient.post('/project/sign-in/urls', param, config);
  return res.data?.data;
}
async function managerSearch(param: Company.ManagerListReq) {
  const res = await ApiClient.post('/member/manager/search', param, config);
  return res.data;
}

async function updateProblemSolved(param: ProblemUpdate) {
  const res = await ApiClient.post(
    '/project/problem/solved/update',
    param,
    config
  );
  return res.data;
}

async function saveLogsByProblemUpdate(param: ProblemLogUpdate) {
  const res = await ApiClient.post('/project/problem/log/save', param, config);
  return res.data;
}

async function bodyParse(param: Analytics) {
  const res = await ApiClient.post('/analytics-util/body/parse', param, config);
  return res.data?.data;
}

async function searchCurrentDeptMapByManager(param: { managerIds: string[] }) {
  const res = await ApiClient.post('/dept/info/current', param, config);
  return res.data;
}

async function searchFirstDeptByManager(param: { managerId: string }) {
  const res = await ApiClient.post('/dept/first/info', param, config);
  return res.data;
}
async function searchDepartmentListByCompanyId(param: { companyId: string, keyword?: string }) {
  const res = await ApiClient.post('/org/inner/dept/search', param, config);
  return res.data;
}
async function searchProjectList(params: { projectIds: string[] }) {
  const res = await ApiClient.post('/project/info/list', params, config);
  return res.data?.data;
}

/** 可能没有太大用处, 优化完推送通知代码 可以删除 */
async function getPushPersonIdsByCompanySet(
  param: Company.pushPersonIdsByCompanySet
) {
  const res = await ApiClient.post('/message/yqzCompanyPushSet', param, config);
  return res.data?.data;
}

/** 可能没有太大用处, 优化完推送通知代码 可以删除 */
async function JMsgPush(param: Company.PushMsg) {
  const res = await ApiClient.post('/message/yqzJMsgPush', param, config);
  return res.data?.data;
}

/** 初始化 faceDB 用的 */
async function managerList(param: {
  companyId: string;
  deleteFlagManager?: 'ALL' | 'N' | 'Y';
}) {
  const res = await ApiClient.post('/member/manager/list', param, config);
  return res.data;
}

/** 本地开发测试用 */
async function jwtSign(param: any) {
  const res = await ApiClient.post('jwt-sign', param, config);
  return res.data;
}

/** 本地开发测试用 */
async function generateManagerToken(param: any) {
  const res = await ApiClient.post('generate-manager-token', param, config);
  return res.data;
}

async function findProjectSignIn(param: {
  companyIds?: string[];
  managerIds?: string[];
  type?: string;
  from?: string;
  to?: string;
  pageNo?: number;
  pageSize?: number;
  isGetCount?: boolean;
}) {
  const res = await ApiClient.post('/project/find/sign-in', param, config);
  return res.data.data;
}

/**
 * 查询人脸库信息
 * @param param 
 * @returns 
 */
async function getOrgFaceSearch(param: {
  companyId?: string;
  _ids: string[];
}) {
  const res = await ApiClient.post('/org/search/face', param, config);
  return res?.data?.data;
}

/**
 * 查询图片注解信息
 * @param param 
 * @returns 
 */
async function searchMediaAnnotationList(param: {
  list: { targetType: string, targetId: string, mediaIdList?: string[], mediaId?: string }[];
}) {
  const res = await ApiClient.post('/media/annotation/search/list', param, config);
  return res?.data?.data;
}

/**
 * 查询个人范围工地列表
 * @param param 
 * @returns 
 */
async function getScopeProjectList(param: { scope: string, personId?: string, companyId?: string, projectIds?: string[] }) {
  const res = await ApiClient.post('/project-util/scope/project/list', param, config);
  return res?.data?.data;
}

/**
 * app签到触发人脸签到结果保存
 * @param param 
 * @returns 
 */
async function saveProjectSignInAppCameraResult(param: SaveAppCameraResultReq): Promise<{ appCameraSignInId: string }> {
  const res = await ApiClient.post('/project/sign-in/app/camera/result/save', param, config);
  return res?.data?.data;
}

/**
 * api调用-查询标签关系列表
 * @param params
 * @returns 
 */
async function searchCompanyTagsForApi(params: { companyId: string, type: string, targetIds?: string[], ids?: string[] }) {
  const res = await ApiClient.post('/company/tag/relation/list', params, config);
  return res?.data?.data;
}

/**
 * api调用-查询标签关系列表V2
 * @param params
 * @returns 
 */
async function searchCompanyTagsForApiV2(params: { companyId: string, type: string, targetIds?: string[], ids?: string[], sortList?: string[], showNum?: string }) {
  const res = await ApiClient.post('/company/tag/relation/v2/list', params, config);
  return res?.data?.data;
}

/**
 * api调用-保存或修改标签
 * @param params 
 * @returns 
 */
async function createOrUpdateCompanyTag(params: SaveTagReqVO): Promise<boolean> {
  const res = await ApiClient.post('/company/tag/relation/save', params, config);
  return res?.data?.data;
}

/**
 * 查询定制公司配置
 * @param params 
 * @returns 
 */
async function getCustomCompanyInfo(params: { companyId: string, customType: string }): Promise<{ companyId: string, customType: string, customConfig: string }> {
  const res = await ApiClient.post('/company/custom/info', params, config);
  return res?.data?.data;
}

/**
 * 查询工地事项配置
 * @param param 
 * @returns 
 */
async function searchMatterSet(param: { companyId: string }) {
  const res = await ApiClient.post('/project/matter/set/search', param, config);
  return res?.data?.data;
}

/**
 * 成员是否是该部门及上级部门负责人
 * @param param 
 * @returns 
 */
async function isDepartmentHeadAndSuperiorByProject(param: { managerId: string, deptId: string }) {
  const res = await ApiClient.post('/dept/check/department-head-superior', param, config);
  return res?.data;
}

/**
 * 根据部门idList查询本部门及子部门IdList信息
 * @param param 
 * @returns 
 */
async function searchSubDeptIdListByDeptIdList(param: { companyId: string, departmentIds: string[], includingSelf: boolean }) {
  const res = await ApiClient.post('/dept/sub/list', param, config);
  return res?.data?.data || [];
}

/**
 * 查询项目归属部门
 * @param param 
 * @returns 
 */
async function projectBelongDept(param: { projectId: string, departmentIds: string[] }): Promise<{ isBelong: boolean }> {
  const res = await ApiClient.post('/project/belong/dept', param, config);
  return res?.data?.data;
}

/**
 * 查询工地阶段节点信息
 * @param param 
 * @returns 
 */
async function getProjectNodes(param: { projectId: string }) {
  const res = await ApiClient.post("/bingohome/node/stage/list", param, config);
  return res?.data?.data as { list: { startTime: string, title: string, rawNodeId: string }[] }
}

/**
 * 查询工地进度筛选
 * @param param 
 * @returns 
 */
async function projectProgressFilter(param: ProjectProgressFilterReq): Promise<string[]> {
  const res = await ApiClient.post("/project/progress/filter", param, config);
  return res?.data?.data;
}

/**
 * 查询工地进度信息
 * @param param 
 * @returns 
 */
async function projectProgressInfo(param: { companyId: string, projectIds: string[] }): Promise<ProjectProgressInfoRes[]> {
  const res = await ApiClient.post("/project/progress/info", param, config);
  return res?.data?.data;
}

/**
 * 工地阶段特征查询
 * @param param 
 * @returns 
 */
async function getStageCharacteristicByProjectId(param: { companyId: string, projectId: string }): Promise<StageCharacteristicByProjectIdRes> {
  const res = await ApiClient.post("/stage/characteristic/search/project", param, config);
  return res?.data?.data;
}

/**
 * 工地进度阶段变更(系统)
 * @param param 
 * @returns 
 */
async function projectProgressStageChange(param: ProjectProgressStageChangeReq): Promise<void> {
  const res = await ApiClient.post("/project/progress/stage/change", param, config);
  return res?.data?.data;
}

/**
 * 工地状态变更
 * @param param 
 * @returns 
 */
async function changeConstructionStatus(param: { projectId: string, status: number }) {
  const res = await ApiClient.post("/project/construction-status/change", param, config);
  return res?.data?.data;
}

/**
 * 获取组织架构树状结构
 * @param params 
 * @returns 
 */
async function orgHirachySearch(params: { companyId: string, managerId: string, listMembers: 'N' | 'Y', departmentId?: string, deep: 'N' | 'Y', checkRole?: string }) {
  const res = await ApiClient.post("/org/hirachy/search", params, config);
  return res?.data?.data;
}

/**
 * 查询工地节点计划（内部服务）
 * @param params 
 * @returns 
 */
async function searchProjectNodePlanList(params: { projectId: string, companyId: string }) {
  const res = await ApiClient.post("/project/node/plan/list", params, config);
  return res?.data;
}

/**
 * 修改工地节点计划（内部服务）
 * @param params 
 * @returns 
 */
async function updateProjectNodePlan(params: { projectId: string, companyId: string, projectStartTime: string, nodeList: { projectNodeName: string, startTime: string, deadlineTime: string }[] }) {
  const res = await ApiClient.post("/project/node/plan/update", params, config);
  return res?.data;
}

/**
 * 删除工地节点计划（内部服务）
 * @param params 
 * @returns 
 */
async function deleteProjectNodePlan(params: { projectId: string, companyId: string }) {
  const res = await ApiClient.post("/project/node/plan/delete", params, config);
  return res?.data;
}

/**
 * 查询工地节点计划变更记录列表（内部服务）
 * @param params 
 * @returns 
 */
async function searchProjectNodePlanLogList(params: { projectId: string, companyId: string }) {
  const res = await ApiClient.post("/project/node/plan/log/list", params, config);
  return res?.data;
}

/**
 * 查询工地信息
 * @param params
 * @returns
 */
async function searchProjectInfo(params: { projectId: string, companyId: string }) {
  const res = await ApiClient.post("/project/info", params, config);
  return res?.data;
}

/**
 * 查询工地成员信息
 * @param params 
 * @returns 
 */
async function searchProjectMemberInfo(params: { projectId: string, companyId: string, roles?: MemberRole[] }) {
  const res = await ApiClient.post("/project/member/info", params, config);
  return res?.data;
}

async function getUserConfig(params: { companyId: string, personId: string, module: string }) {
  const res = await ApiClient.post("/user-cfg/get", params, config);
  return res?.data;
}

async function getProjectProblemListStatus(params: {
  problemSourceId: number,
  problemSourceType: string
}) {
  const res = await ApiClient.post("/bingohome/project/problem/detail/list/search", params, config);
  return res?.data?.data;
}

async function getProjectProblemStatus(params: {
  problemSourceId: number,
  problemSourceType: string
}) {
  const res = await ApiClient.post("/bingohome/project/problem/detail/search", params, config);
  return res?.data?.data;
}

/**
 * 无验收标准保存草稿
 * @param params 
 * @returns 
 */
async function saveDraftNoCriteria(params: SaveDraftNoCriteriaVO) {
  const res = await ApiClient.post("/acceptance/report/draft/no-criteria/save", params, config);
  return res?.data;
}

/**
 * 删除其他相同名称的验收报告草稿
 * @param params 
 * @returns 
 */
async function deleteOtherSameReportDraft(params: { companyId: string, projectId: string, acceptanceReportName: string }) {
  const res = await ApiClient.post("/acceptance/report/draft/delete/same", params, config);
  return res?.data;
}

/**
 * 验收报告查询详情
 * @param params 
 * @returns 
 */
async function getAcceptanceReportDetail(params: { acceptanceReportId: string, acceptanceApprovalRecordId?: string, type?: 'detail' | 'approval_detail' }) {
  const res = await ApiClient.post("/acceptance/report/detail", params, config);
  return res?.data?.data;
}

/**
 * 验收报告修改(无需登录)
 * @param params
 */
async function modifyReportByApi(params: ReportUpdateVO) {
  const res = await ApiClient.post("/acceptance/report/api/modify", params, config);
  return res?.data;
}

/**
 * 验收审核提交(无需登录)
 * @param params 
 * @returns 
 */
async function approvalExecuteByApi(params: HandleApproveByApiVO) {
  const res = await ApiClient.post("/approval/api/commit", params, config);
  return res?.data;
}

/**
 * 同步验收报告草稿进度识别图片
 * @param params 
 * @returns 
 */
async function syncDraftStageMedia(params: { projectId: string, stage: string, addCheckStageMediaList: YqzMedia[] }) {
  const res = await ApiClient.post("/acceptance/report/draft/sync/stage/media", params, config);
  return res?.data;
}

/**
 * 获取工地更新默认事件
 * @param params 
 */
async function getFirstDefaultEvent(params: { projectId: string }) {
  const res = await ApiClient.post("/project/update/first/default/event", params, config);
  return res?.data;
}

/**
 * 同步工地成员验收报告草稿
 * @param params 
 * @returns
 */
async function syncMemberAcceptanceReportDraft(params: { companyId: string, projectId: string, personId: string }) {
  const res = await ApiClient.post("/acceptance/report/draft/sync/member", params, config);
  return res?.data;
}