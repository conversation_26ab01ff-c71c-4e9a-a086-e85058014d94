// 不能直接import否则会报axios undefined
// import Axios from "axios"
// https://github.com/axios/axios/issues/5044
// https://github.com/axios/axios/pull/5162
import Axios = require('axios');
import * as uuid from 'uuid';
import { MyLogger } from '../../nestjs';
import { ReqHeader } from '../../types/req-header.enum';
const logger = new MyLogger('ApiClient');
const ApiClient: Axios.AxiosInstance = (Axios as any).create({ maxBodyLength: Infinity });

const logging = process.env.YQZ_API_LOGGING !== 'N';
if (!process.env.YQZ_TARGET_ENV)
  throw new Error(`[ApiClient] process.env.YQZ_TARGET_ENV is not defined`);

// 线上调用不需要用公网域名
// ApiClient.defaults.baseURL =
//   process.env.YQZ_TARGET_ENV === 'prd'
//     ? 'http://cdn.1qizhuang.com'
//     : 'http://api-dev.1qizhuang.com';

ApiClient.interceptors.request.use(
  function (config) {
    const uid = uuid.v4().replace(/-/g, '');
    if (!config.headers) config.headers = {};

    config.headers[ReqHeader.YqzApiUid] = uid;
    if (process.env.YQZ_SERVICE_USER) {
      config.headers[ReqHeader.YqzUser] = process.env.YQZ_SERVICE_USER;
    } else {
      logger.warn(`process.env.YQZ_SERVICE_USER is not defined`);
    }

    if (logging)
      logger.log(
        uid,
        config.method,
        config.baseURL,
        config.url,
        config.params ? JSON.stringify(config.params) : '',
        config.data ? JSON.stringify(config.data) : ''
      );

    if (config.method === 'post') {
    }

    if (config.method === 'get') {
    }

    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

ApiClient.interceptors.response.use(
  function (response) {
    if (logging) {
      const uid =
        response.config.headers && response.config.headers[ReqHeader.YqzApiUid];
      logger.log(
        uid,
        'res',
        response.data ? JSON.stringify(response.data) : ''
      );
    }

    return response;
  },
  function (error) {
    return Promise.reject(error);
  }
);

export { ApiClient };
