import { AxiosRequestConfig } from 'axios';
import { ApiClient } from './api-client';
import { Message } from '../types';

const config: AxiosRequestConfig = Object.freeze({
  baseURL: process.env.YQZ_BINGOHOME_API,
});

export const BingoHomeApi = {
  saveDeliveryMessage,
  getTargetContent,
  addMemberToProject,
  filterCompany
};

/**
 * 保存交付消息内容到数据库 redis
 * @param
 * @returns
 */
async function saveDeliveryMessage(param: Message.SaveDeliveryMessage) {
  const res = await ApiClient.post('/message/delivery/save', param, config);
  return res.data;
}

/**
 * 查询积分targetType
 * @param params
 * @returns
 */
async function getTargetContent(
  params: {
    targetId: string;
    targetType: string;
    creditId: string;
    shared: boolean;
  }[]
) {
  const res = await ApiClient.post('/target/content', params, config);
  return res.data.data;
}

/**
 * 添加成员到工地
 */
async function addMemberToProject(
  params: {
    projectId: string;
    personId: string;
    operationPersonId: string;
  }
) {
  const res = await ApiClient.post('/project-member/v2/save', params, config);
  return res.data;
}

/**
 * 根据条件过滤公司
 * @param params 
 * @returns 
 */
async function filterCompany(
  params: {
    businessCategory?: string;
    companyIds?: string[];
  }
) {
  const res = await ApiClient.post('/company/filter', params, config);
  return res.data;
}