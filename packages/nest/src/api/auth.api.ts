import { AxiosRequestConfig } from 'axios';
import { ApiClient } from './api-client';
import { CanReq } from '../types/auth.type';

const config: AxiosRequestConfig = {
    baseURL: process.env.YQZ_AUTH_API,
};

export const AuthApi = {
    can,
    userRoleSearch,
};

/**
 * 查询是否有权限
 * @param data
 * @returns
 */
async function can(data: CanReq) {
    const res = await ApiClient.post('/can', data, config);
    return res?.data?.data?.pass;
}

async function userRoleSearch(data: any) {
    const res = await ApiClient.post('/admin/user_role/search', data, config)
    return res?.data?.data;
}