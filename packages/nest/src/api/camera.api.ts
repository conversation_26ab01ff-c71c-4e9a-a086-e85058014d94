import { AxiosRequestConfig } from 'axios';
import { ApiBaseRes } from '../types/api.type';
import { ApiClient } from './api-client';
import { CompanyAiSet, DeviceManagerBatchUpdateReq, ProjectDeviceRealTimeInfoResDTO, SaveInfoReq, SearchFaceReq } from '../types/camera.type';
import { YqzMedia } from '../types/media.type';
const config: AxiosRequestConfig = {
  baseURL: process.env.YQZ_CAMERA_SERVICE_API,
};

export const CameraApi = {
  getProjectBindIngList,
  addALiFace,
  deleteALiFace,
  projectDeviceStatus,
  searchBindProjectInfo,
  detectionsCountInfo,
  searchDeviceAiInfoList,
  searchDeviceStatus,
  updatePassword,
  updateDeviceRemark,
  deviceBindProjectChange,
  getEventPhotoIdListInfo,
  triggerCaptureEvent,
  searchBodyUrlByEventIds,
  faceSearch,
  unbindingProjectList,
  bodyAnalytics,
  bodyAnalytics_v2,
  bodyAnalyticsByPerson,
  bodyAnalyticsByRole,
  bodyAnalyticsList,
  searchTagListByCompany,
  getSubTags,
  searchTagsFirstTime,
  memberSync,
  searchContractList,
  cameraFaceDbUpdate,
  findProjectInfoStatistics,
  searchFilterProject,
  getProjectDeviceRealTimeInfo,
  getCompanyAiSet,
  updateCompanyAiSet,
  searchProblemDetectionOptions,
  searchDetectionMapping,
  searchDetectionProjectOptions,
  searchDeviceDeptList,
  searchProjectDeviceDeptList,
  searchDeviceOffOnlineEvent,
  searchProjectTidinessDetectionDetail,
  searchLateEntryList,
  searchCompanyDetectionProjectDetail,
  searchDeviceList,
  projectBindingNum,
  handoverDeviceIdList,
  handoverDeviceList,
  deviceManagerBindingBatch,
  getCompanyIdsByProjectProgressDetectionTime,
  searchProjectProgressMediaByProjectId
};

async function getProjectBindIngList(companyId: string) {
  const res = await ApiClient.post<
    ApiBaseRes<{
      bindingProjectIds: { count: number; rows: { projectId: string }[] };
    }>
  >('/devices/project/binding/list', { companyId }, config);
  return res.data;
}

async function searchFilterProject(params: {
  companyId?: string;
  projectIds?: string[];
  isOnline?: string;
  isBind?: string;
}) {
  const res = await ApiClient.post('/devices/filter/project/list', params, config);
  return res.data;
}

/**
 * 阿里添加人脸
 * @param param0
 * @returns
 */
async function addALiFace({
  managerId,
  imageUrl,
  info
}: {
  managerId: string;
  imageUrl: string;
  info?: SaveInfoReq;
}) {
  const res = await ApiClient.post(
    '/cloud/face/add',
    { managerId, imageUrl, info },
    config
  );
  return res.data;
}

/**
 * 阿里删除人脸
 * @param param0
 * @returns
 */
async function deleteALiFace({
  managerId,
  companyId,
}: {
  managerId: string;
  companyId: string;
}) {
  const res = await ApiClient.post(
    '/cloud/face/delete',
    { managerId, companyId },
    config
  );
  return res.data;
}

/**
 * 查询设备信息
 * @param param0
 * @returns
 */
async function projectDeviceStatus({ projectId }: { projectId: string }) {
  const res = await ApiClient.post(
    '/devices/project/status',
    { projectId },
    config
  );
  return res.data;
}

/**
 * 查询检测统计信息
 * @param param0
 * @returns
 */
async function detectionsCountInfo({
  companyId,
  projectId,
  startTime,
  endTime,
}: {
  companyId: string;
  projectId?: string;
  startTime?: string;
  endTime?: string;
}) {
  const res = await ApiClient.post(
    '/detections/count/info',
    { companyId, projectId, startTime, endTime },
    config
  );
  return res.data;
}

async function searchBindProjectInfo(params: {
  companyId?: string;
  noWorkDays?: string;
  sevenDayTidinessType?: string[];
  projectList?: string[];
  isOnline?: string;
  isBind?: string;
  pageNo?: number;
  pageSize?: number;
}) {
  const res = await ApiClient.post(
    '/devices/bind/project/info',
    params,
    config
  );
  return res.data;
}

async function searchDeviceAiInfoList(params: {
  companyId: string;
  projectIdList?: string[];
  sevenDayTidinessType?: string[];
  noWorkDays?: string;
}) {
  const res = await ApiClient.post(
    '/devices/project/aiinfo/list',
    params,
    config
  );
  return res.data;
}

/**
 * 查询设备状态信息
 * @param yqzDeviceId
 * @returns
 */
async function searchDeviceStatus({ yqzDeviceId }: { yqzDeviceId: string }) {
  const res = await ApiClient.post('/devices/status', { yqzDeviceId }, config);
  return res.data.data;
}

/**
 * 更新设备播放密码
 * @param yqzDeviceId
 * @returns
 */
async function updatePassword({
  yqzDeviceId,
  videoPassword,
}: {
  yqzDeviceId: number;
  videoPassword: string;
}) {
  const res = await ApiClient.post(
    '/devices/password/update',
    { yqzDeviceId, videoPassword },
    config
  );
  return res.data;
}

/**
 * 更新设备备注
 * @param yqzDeviceId
 * @returns
 */
async function updateDeviceRemark({
  yqzDeviceId,
  deviceRemark,
}: {
  yqzDeviceId: string;
  deviceRemark: string;
}) {
  const res = await ApiClient.post(
    '/devices/remark/update',
    { yqzDeviceId, deviceRemark },
    config
  );
  return res.data;
}

/**
 * 设备绑定工地变更
 * @param yqzDeviceId
 * @returns
 */
async function deviceBindProjectChange({
  yqzDeviceId,
  projectId,
  managerId,
  companyId,
}: {
  yqzDeviceId: string;
  projectId: string;
  companyId?: string;
  managerId?: string;
}) {
  const res = await ApiClient.post(
    '/device/update/bind-project/change',
    { yqzDeviceId, projectId, managerId, companyId },
    config
  );
  return res.data;
}

/**
 * 查询eventIdList Info
 * @param eventIds
 * @returns
 */
async function getEventPhotoIdListInfo({ eventIds }: { eventIds: number[] }) {
  const res = await ApiClient.post(
    '/motiondetect/photos/info',
    { eventIds },
    config
  );
  return res.data.data;
}

/**
 * 校验抓拍设置，获取抓拍图片
 * @param eventIds
 * @returns
 */
async function triggerCaptureEvent({
  projectId,
  companyId,
  event,
  sources,
  sourceId,
}: {
  projectId: string;
  companyId: string;
  event?: string;
  sources?: { source: string; type?: string }[];
  sourceId?: string;
}) {
  const res = await ApiClient.post(
    '/ai-set/capture/event',
    { projectId, companyId, event, sources, sourceId },
    config
  );
  return res.data.data;
}

/**
 * 查询bodyUrl
 * @param eventIds
 * @returns
 */
async function searchBodyUrlByEventIds({
  projectId,
  eventIds,
}: {
  projectId: string;
  eventIds: string[];
}) {
  const res = await ApiClient.post(
    '/face/api/bodyurl/list',
    { projectId, eventIds },
    config
  );
  return res.data.data;
}

/**
 * 查询人脸分类列表
 * @param companyId
 * @returns
 */
async function searchTagListByCompany({ companyId }: { companyId: string }) {
  const res = await ApiClient.post(
    '/face/tag/tags/list',
    { companyId },
    config
  );
  return res.data.data;
}

/**
 * 查询人脸分类子列表
 * @param companyId
 * @returns
 */
async function getSubTags({
  companyId,
  tagList,
}: {
  companyId: string;
  tagList: string[];
}) {
  const res = await ApiClient.post(
    '/face/api/subTags',
    { companyId, tagList },
    config
  );
  return res.data.data;
}

/**
 * 查询 人脸库查询
 * @param eventIds
 * @returns
 */
async function faceSearch(body: SearchFaceReq) {
  const res = await ApiClient.post('/face/api/list', body, config);
  return res.data.data;
}

/**
 * 解绑工地下全部设备
 * @param projectId
 * @param operator
 * @returns
 */
async function unbindingProjectList({
  projectId,
  operator,
}: {
  projectId: string;
  operator: string;
}) {
  const res = await ApiClient.post(
    '/devices/unbinding/project/list',
    { projectId, operator },
    config
  );
  return res.data;
}

async function bodyAnalytics(params: {
  companyId: string;
  faceEntityIds: string[];
  projectIds?: string[];
  from?: string;
  to?: string;
  options?: {
    pageNo?: number;
    pageSize?: number;
  };
}) {
  const res = await ApiClient.post('/body/api/analytics', params, config);
  return res.data.data;
}
async function bodyAnalytics_v2(params: {
  companyId: string;
  faceEntityIds: string[];
  projectIds?: string[];
  from?: string;
  to?: string;
  options?: {
    method?: 'POSITION' | 'PERSON' | 'LIST';
    pageNo?: number;
    pageSize?: number;
  };
}) {
  const res = await ApiClient.post('/body/api/analytics/v2', params, config);
  return res.data.data;
}

/**
 * 获取工地下每个tag最早入场时间
 * @param params
 * @returns
 */
async function searchTagsFirstTime(params: {
  projectId: string;
  tags: string[];
}) {
  const res = await ApiClient.post('/face/api/tags/first-time', params, config);
  return res.data?.data || [];
}

/**
 * manger修改同步face、body
 * @param params
 * @returns
 */
async function memberSync(params: {
  companyId: string;
  faceEntityId: string;
  tag: string;
  name?: string;
}) {
  const res = await ApiClient.post('/face/api/member/sync', params, config);
  return res.data;
}

/**
 * 获取合同列表
 * @param params
 * @returns
 */
async function searchContractList(params: { companyId: string }) {
  const res = await ApiClient.post('/contract/list', params, config);
  return res.data?.data;
}

/**
 * 修改FaceDb
 * @param params
 * @returns
 */
async function cameraFaceDbUpdate(params: {
  companyId: string;
  faceEntityId: string;
  tags?: string;
  name?: string;
  refreshBodyTags?: boolean;
}) {
  const res = await ApiClient.post('/face/api/face/update', params, config);
  return res.data?.data;
}


/**
 * 查询工地统计信息
 * @param params
 * @returns
 */
async function findProjectInfoStatistics(params: {
  companyId: string;
  projectId: string;
}) {
  const res = await ApiClient.post('/project/info/statistics', params, config);
  return res.data?.data;
}

/**
 * 查询工地设备实时信息
 * @param params 
 * @returns 
 */
async function getProjectDeviceRealTimeInfo(params: {
  projectId: string;
}): Promise<ProjectDeviceRealTimeInfoResDTO> {
  const res = await ApiClient.post('/project/device/real-time/info', params, config);
  return res?.data?.data;
}

/**
 * 查询公司ai设置
 * @param companyId 
 * @returns 
 */
async function getCompanyAiSet(companyId: string): Promise<CompanyAiSet> {
  const res = await ApiClient.post('/ai-set/config/search', { companyId }, config);
  return res?.data?.data;
}

/**
 * 修改公司ai设置
 * @param companyId 
 * @returns 
 */
async function updateCompanyAiSet(params: { isCustomSignInDistance?: string, signInDistance?: number }) {
  const res = await ApiClient.post('/ai-set/config/update', params, config);
  return res?.data;
}

/**
 * 查询工地问题检测选项
 * @param param
 * @returns
 */
async function searchProblemDetectionOptions(param: { types?: string[], isTree?: boolean, companyId?: string }) {
  const res = await ApiClient.post(
    '/detections/problem/options',
    param,
    config
  );
  return res.data?.data;
}

/**
 * 查询检测映射关系
 * @param param
 * @returns
 */
async function searchDetectionMapping(param: { values: string[] }) {
  const res = await ApiClient.post(
    '/detections/problem/mapping',
    param,
    config
  );
  return res.data?.data;
}

/**
 * 公司/工地检测项目选项
 * @param param 
 * @returns 
 */
async function searchDetectionProjectOptions(param: { companyId?: string, projectId?: string }) {
  const res = await ApiClient.post(
    '/ai-set/detection/options',
    param,
    config
  );
  return res.data?.data;
}

/**
 * 查询公司检测项目
 * @param param 
 * @returns 
 */
async function searchCompanyDetectionProjectDetail(param: { companyId: string }): Promise<{ companyId: string, detectionProjects: string[] }> {
  const res = await ApiClient.post(
    '/ai-set/company/detection/detail',
    param,
    config
  );
  return res.data?.data;
}

/**
 * 查询设备部门归属
 * @param param 
 * @returns 
 */
async function searchDeviceDeptList(param: { companyId: string, projectIds?: string[], departmentId?: string }) {
  const res = await ApiClient.post(
    '/devices/dept/project/list',
    param,
    config
  );
  return res.data;
}

/**
 * 查询工地设备部门归属
 * @param params 
 * @returns 
 */
async function searchProjectDeviceDeptList(params: {
  projectIds?: string[];
  departmentId?: string;
}): Promise<ProjectDeviceRealTimeInfoResDTO> {
  const res = await ApiClient.post('project/devices/dept/list', params, config);
  return res?.data?.data;
}

/**
 * 查询离线事件详情
 * @param param 
 * @returns 
 */
async function searchDeviceOffOnlineEvent(param: { id: string }) {
  const res = await ApiClient.post(
    '/device/offline/event/search/detail',
    param,
    config
  );
  return res.data;
}

/**
 * 查询工地整洁检测详情
 * @param param 
 * @returns 
 */
async function searchProjectTidinessDetectionDetail(param: { id: string }) {
  const res = await ApiClient.post(
    '/detections/tidiness/search/detail',
    param,
    config
  );
  return res.data?.data;
}

/**
 * 查询派派工工人逾期未进场列表
 * @param params 
 */
async function searchLateEntryList(params: { companyId: string, dispatchWorkerOverdueDay?: number }) {
  const res = await ApiClient.post(
    '/rp/dispatch/late/entry/list',
    params,
    config
  );
  return res.data?.data;
}
/*
  * 查询设备列表/devices/search
 */
async function searchDeviceList(params: { companyId: string, departmentId: string, }) {
  const res = await ApiClient.post(
    '/devices/search',
    params,
    config
  )
  return res.data;
}

async function projectBindingNum(projectId: string) {
  const res = await ApiClient.post(
    '/devices/project/bind/count',
    { projectId },
    config
  )
  return res.data?.data;
}

/**
 * 查询交接资源 - 设备Id列表
 * @param params 
 * @returns 
 */
async function handoverDeviceIdList(params: { companyId: string, operateManagerId: string, initiatorId: string, deviceSerial?: string, deviceDepartmentId?: string }) {
  const res = await ApiClient.post('/devices/handover/device/id/list', params, config)
  return res.data?.data || [];
}

/**
 * 查询交接资源 - 设备列表
 * @param params 
 * @returns 
 */
async function handoverDeviceList(params: { companyId: string, operateManagerId: string, initiatorId: string, deviceSerial?: string, deviceDepartmentId?: string }) {
  const res = await ApiClient.post('/devices/handover/device/list', params, config)
  return res.data?.data || [];
}

/**
 * 更新设备领用人(批量)
 * @param params 
 * @returns 
 */
async function deviceManagerBindingBatch(params: DeviceManagerBatchUpdateReq): Promise<{
  success: string[];
  fail: string[];
}> {
  const res = await ApiClient.post('/devices/binding/manager/batch', params, config)
  return res.data?.data;
}

/**
 * 根据施工进度检测时间查询公司id列表
 * @param params 
 * @returns 
 */
async function getCompanyIdsByProjectProgressDetectionTime(params: { projectProgressCaptureTime: string, companyIds?: string[] }) {
  const res = await ApiClient.post('/ai-set/progress/detection/company/list', params, config)
  return res.data?.data || [];
}

/**
 * 根据项目id查询工地进度识别图片
 * @param params 
 * @returns 
 */
async function searchProjectProgressMediaByProjectId(params: { companyId: string, projectId: string }): Promise<{ [key: string]: YqzMedia[] }> {
  const res = await ApiClient.post('/detections/project-progress/media/search', params, config);
  return res?.data?.data || {};
}

/**
 * 现场巡检员工分布
 * @param params 
 * @returns 
 */
async function bodyAnalyticsByPerson(params: { companyIds: string[]; faceEntityIds: string[]; projectIds?: string[]; from: string; to: string; faceIdentifyTypes?: string[]; tags?: string[]; }) {
  const res = await ApiClient.post('/body/api/analytics/person', params, config);
  return res?.data?.data;
}

/**
 * 现场巡检岗位分布
 * @param params 
 * @returns 
 */
async function bodyAnalyticsByRole(params: { companyIds: string[]; faceEntityIds: string[]; projectIds?: string[]; from: string; to: string; faceIdentifyTypes?: string[]; tags?: string[]; }) {
  const res = await ApiClient.post('/body/api/analytics/role', params, config);
  return res?.data?.data;
}

/**
 * 现场巡检列表
 * @param params 
 * @returns 
 */
async function bodyAnalyticsList(params: {
  companyIds: string[]; faceEntityIds: string[]; projectIds?: string[]; from: string; to: string; faceIdentifyTypes?: string[]; tags?: string[];
  options?: {
    pageNo?: number;
    pageSize?: number;
  };
}) {
  const res = await ApiClient.post('/body/api/analytics/list', params, config);
  return res?.data?.data;
}