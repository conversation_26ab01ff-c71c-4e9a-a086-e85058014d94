import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { MyRedis } from '../../redis/my-redis';
import { YqzException } from '../filters';
import * as _ from 'lodash';
import { MyThrottle } from '../decorators/my-throttle.decorator';

@Injectable()
export class ThrottleGuard implements CanActivate {
    constructor(private reflector: Reflector) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const req = context.switchToHttp().getRequest();
        const handler = context.getHandler();

        const throttleConfig = this.reflector.getAllAndOverride<MyThrottle.Params>(
            MyThrottle.Key,
            [handler, context.getClass()]
        );

        if (!throttleConfig) {
            return true;
        }

        const { key, limit = 60, ttl = 60, error } = throttleConfig;
        let values = '';

        // 构建Redis key
        if (_.isString(key)) {
            values = key;
        } else if (_.isFunction(key)) {
            values = key();
        } else if (_.isArray(key)) {
            key?.map((res) => {
                const params = req.body;
                if (!_.isArray(res.fields) || _.isEmpty(res.fields)) {
                    values += JSON.stringify(params);
                } else {
                    const map = {};
                    res.fields.map((field) => {
                        if (undefined != params[field]) map[field] = params[field];
                    });
                    values += !_.isEmpty(map) ? JSON.stringify(map) : JSON.stringify(params);
                }
            });
        }

        const throttleKey = values
            ? `throttle:${req.route.path}:${values}`
            : `throttle:${req.route.path}`;

        // 获取当前时间窗口的记录
        const record = await MyRedis.get(throttleKey);
        const currentTime = Date.now();
        let requestCount = 0;
        let timeWindow = currentTime;

        if (record) {
            const [storedCount, storedWindow] = record.split(':').map(Number);
            requestCount = storedCount;
            timeWindow = storedWindow;

            // 检查是否在同一个时间窗口内
            if (currentTime - timeWindow >= ttl * 1000) {
                // 超出时间窗口，重置计数
                requestCount = 0;
                timeWindow = currentTime;
            } else if (requestCount >= limit) {
                // 在时间窗口内且超过限制
                const remainingTime = Math.ceil((timeWindow + ttl * 1000 - currentTime) / 1000);
                throw new YqzException({
                    code: error?.code || '429',
                    msg: error?.message || `请求过于频繁，请在${remainingTime}秒后重试`,
                });
            }
        }

        // 更新计数和时间窗口
        requestCount++;
        await MyRedis.set(
            throttleKey,
            `${requestCount}:${timeWindow}`,
            ttl
        );

        return true;
    }
} 