import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';
import * as _ from 'lodash';
import { RequireAuth } from '../decorators';
import { AuthScope } from '../decorators/require-auth.decorator';
import { YqzException } from '../filters';
import { JwtUserV22, ReqHeader } from '../../types';
import { USER_KEY } from '../middlewares';
import { AuthApi } from '../..';
import { MyLogger } from '../modules';
import * as uuid from 'uuid';

export const AuthPassKey = '_passauth';
export const isSimAuth = (req: any) => Number(req.body?._simauth || req.query?._simauth) === 1;

/**
 * 权限校验
 * 会在拦截器之前执行
 */
@Injectable()
export class AuthGuard implements CanActivate {
  private logger = new MyLogger(AuthGuard.name);

  constructor(private reflector: Reflector) { }

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const req = context.switchToHttp().getRequest();
    const checkList = this.reflector.getAllAndOverride<RequireAuth.RequireAuthParams>(
      RequireAuth.Key,
      [context.getHandler(), context.getClass()]
    );
    if (!checkList) return true;
    return this.checkAuth({ req, checkList });
  }

  /**
   * 校验权限
   * @param param0 
   * @returns boolean
   */
  async checkAuth({ req, checkList }: { req: any, checkList: RequireAuth.RequireAuthParams }) {
    const start = new Date().getTime();
    req[AuthPassKey] = 1;//默认权限校验通过
    const IsSimMode = isSimAuth(req);//是否只做权限校验, 不执行后续代码,也不会报错（接口要携带_simauth参数）
    for (const { subjectType, idGetter, dataGetter, action, handling = 'default' } of checkList) {
      const body = req.body;
      const query = req.query;
      // 1、提取subjectId
      let subjectId = '';
      if (idGetter) {
        if (typeof idGetter === 'string') subjectId = body[idGetter] || query[idGetter];
        else if (typeof idGetter === 'function') subjectId = idGetter(req);
        if (!subjectId) throw new YqzException(`YqzAuthGuard failed to execute idGetter`);
      }
      // 2、提取subjectData
      let subjectData: Partial<AuthScope> | undefined = undefined;
      if (dataGetter) {
        if (typeof dataGetter === 'function') subjectData = dataGetter(req);
        if (!subjectData) throw new YqzException(`YqzAuthGuard failed to execute dataGetter`);
      }
      // 3、校验权限
      const pass = await this.faceRequireAuth({ user: req[USER_KEY], subjectId, subjectData, subjectType, action, body, query });
      // 4、处理权限校验结果
      if (!pass) {
        req[AuthPassKey] = -1;//设置header, SimAuthInterceptor会读取, 在接口用作权限校验时返回该值
        const key = req?.headers[ReqHeader.YqzReqId] || uuid.v4().replace(/-/g, '');
        const user = req?.headers[ReqHeader.YqzUser] || req?.headers[ReqHeader.JwtPayload] || 'anonymous'
        this.logger.log(key, `${new Date().getTime() - start}ms`, req.method, req.url, 'user:', user, 'body:', _.isEmpty(req.body) ? 'null' : JSON.stringify(req.body), `check auth false pass: ${pass}`);
      }
      try {
        if (typeof handling === 'function') handling(pass, req);
        if (!pass && !IsSimMode) return false;//权限校验不通过
      } catch (err) {
        if (!IsSimMode) throw err;//如果业务接口作为权限校验不需要抛异常，作为业务接口则需要抛出不执行后续操作
      }
      // if (!handling || handling === 'default') {
      //   if (!pass) {
      //     if (!IsSimMode) return false;
      //     else req[AuthPassKey] = -1; //设置header,SimAuthInterceptor 会读取
      //   }
      // } else {
      //   // 否则不处理，让回调函数决定如何处理
      //   if (typeof handling === 'function') {
      //     try {
      //       handling(pass, req);
      //     } catch (err) {
      //       if (!IsSimMode) throw err;
      //       else req[AuthPassKey] = -1; //设置header,SimAuthInterceptor 会读取
      //     }
      //   }
      // }
    }
    return true;
  }

  /**
   * 权限校验
   * @param params 
   * @returns boolean
   */
  async faceRequireAuth(params: { user: JwtUserV22, action: any, subjectId: string, subjectType: any, subjectData: any, body: any, query: any }) {
    // console.log(`facecheckauth: ${JSON.stringify(params)} ${params.user.userType}`);
    const { user, action, subjectId, subjectData, subjectType, body, query } = params;
    if (user.userType === 'yqz-service') return true;//服务之间调用无需校验权限
    if (user.userType === 'yqz') return true;//yqz-admin无需校验权限
    if (user.userType !== 'worker' && user.userType !== 'owner') return false;
    const companyId = String(user?.targetCompany?.companyId || body?.companyId || query?.pl_company || "");//目前前端业主不会传token
    //业主token不会有companyId,所以需要从参数中获取
    // if (user.userType === 'owner') companyId = String(user.targetCompany?.companyId || body?.companyId || query?.pl_company || "");
    //调用权限校验
    return await AuthApi.can({ user: { companyId, personId: String(user.personId) }, action, subjectId, subjectType, subjectData });
  }

}
