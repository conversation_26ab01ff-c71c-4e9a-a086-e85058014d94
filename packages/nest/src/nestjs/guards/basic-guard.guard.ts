import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';

import * as _ from 'lodash';
import { MyLogger } from '../modules';
import { RequireLogin } from '../decorators';
import { USER_GW__KEY, USER_KEY } from '../middlewares';

/**
 * 登录校验
 * 会在拦截器之前执行
 */
@Injectable()
export class BasicAuthGuard implements CanActivate {
  private logger = new MyLogger(BasicAuthGuard.name);
  constructor(private reflector: Reflector) {}

  canActivate(
    context: ExecutionContext
  ): boolean | Promise<boolean> | Observable<boolean> {
    const req = context.switchToHttp().getRequest();

    const requireLogin = this.reflector.getAllAndOverride<RequireLogin.Params>(
      RequireLogin.Key,
      [context.getHandler(), context.getClass()]
    );
    // this.logger.log(`path:${req.path}, requireLogin: ${requireLogin}`)
    if (requireLogin === undefined || requireLogin === 'N') return true;

    // requireLogin='Y'
    if (
      (!req[USER_KEY] || _.isEmpty(req[USER_KEY])) &&
      (!req[USER_GW__KEY] || _.isEmpty(req[USER_GW__KEY]))
    ) {
      this.logger.log(`access to ${req.path} blocked, missing user`);
      return false;
    }

    return true;
  }
}
