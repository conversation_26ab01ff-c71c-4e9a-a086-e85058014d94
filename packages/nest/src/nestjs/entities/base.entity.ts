import { Column, BeforeInsert, BeforeUpdate } from 'typeorm';
import { DeleteFlag } from '../../types/delete-flag.enum';

export abstract class Base {
  @Column({ select: false })
  delete_flag: DeleteFlag;

  @Column({ nullable: false, select: false })
  createTime: Date;

  @Column({ select: false })
  updateTime: Date;

  @Column({ select: false })
  createBy: string;

  @Column({ select: false })
  updateBy: string;

  @BeforeInsert()
  beforeInsert() {
    this.createTime = new Date();
    this.updateTime = this.updateTime || new Date();
  }

  @BeforeUpdate()
  beforeUpdate() {
    this.updateTime = new Date();
  }
}
