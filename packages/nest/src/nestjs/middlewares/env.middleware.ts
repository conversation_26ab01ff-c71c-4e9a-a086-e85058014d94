import { Request, Response, NextFunction } from 'express';
import * as shelljs from 'shelljs';
import * as _ from 'lodash';

export function getEnvMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  if (req.path === '/env') {
    const rawEnv = shelljs.exec('env', { silent: true });
    const exclude: RegExp[] = [
      /^\s*$/,
      /^npm/,
      /^TERM/,
      /^ZSH/,
      /^VSCODE/,
      /^PATH=/,
      /^COLORTERM=/,
      /^SHELL=/,
      /^TMPDIR=/,
      /^CONDA_.+/,
      /^NVM/,
      /^NODE_VERSION=/,
      /^HOSTNAME=/,
      /^YARN_VERSION=/,
      /^SHLVL/,
      /^HOME=/,
      /^NODE=/,
      /^PWD=/,
      /^INIT_CWD=/,
      /^ZDOTDIR=/,
      /^ORIGINAL_XDG_CURRENT_DESKTOP=/,
      /^MallocNanoZone=/,
      /^USER=/,
      /^COMMAND_MODE=/,
      /^SSH_AUTH_SOCK=/,
      /^_/,
      /^LANG=/,
      /^GIT_/,
      /^PAGER=/,
      /^LSCOLORS=/,
      /^XPC_FLAGS=/,
      /^LESS=/,
      /^LOGNAME=/,
      /^XPC_SERVICE_NAME=/,
    ];
    const envList = rawEnv
      .split('\n')
      .filter((e) => exclude.every((reg) => !reg.test(e)))
      .map((e) => {
        const [key, value] = e.split('=');
        const checkList = [
          /(user|USER).*=/,
          /password|PASSWORD/,
          /token|TOKEN/,
          /secret|SECRET/,
          /(pwd|PWD)=/,
        ];
        if (checkList.some((reg) => reg.test(e))) return [key, '*************'];
        return [key, value];
      });
    res.setHeader('content-type', 'application/json');
    return res.end(JSON.stringify(_.fromPairs(envList)));
  }
  next();
}
