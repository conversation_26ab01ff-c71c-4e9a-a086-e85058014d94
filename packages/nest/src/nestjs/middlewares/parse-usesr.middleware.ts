import { Request, Response, NextFunction } from 'express';
import { ReqHeader } from '../../types';

export const USER_KEY = 'user';
export const USER_GW__KEY = 'user_gw';

export function paseUserMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  req[USER_KEY] = {};
  req[USER_GW__KEY] = {};
  try {
    const userV2 = req.headers[ReqHeader.YqzUser];
    if (typeof userV2 === 'string') {
      req[USER_KEY] = JSON.parse(userV2);
    }

    const gwUser = req.headers[ReqHeader.JwtPayload];
    if (typeof gwUser === 'string') {
      req[USER_GW__KEY] = JSON.parse(gwUser);
    }
  } catch (err) {}

  next();
}
