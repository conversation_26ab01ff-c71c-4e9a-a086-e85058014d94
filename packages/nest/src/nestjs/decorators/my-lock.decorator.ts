import { MyRedis } from '../../redis/my-redis';
import { YqzException } from '../filters';
import * as _ from 'lodash';

export { MyDebounce };

/**
 * 防抖
 * key示例: 1、key为字符串 => @MyDebounce('test')
 *         2、key为数组,方法名作为key => @MyDebounce([])
 *         3、key为数组,方法名有多个入参数,第一个参数所有变量作为key => @MyDebounce([{ paramIndex: 0, fields: [] }])
 *         4、key为数组,方法名有多个入参数,第一个参数中的某几个变量作为key => @MyDebounce([{ paramIndex: 0, fields: ['变量名',...] }])
 * 注:所有定义的key前缀都有会方法名
 * @param key index:方法的第几个参数,fields:参数中的元素;key为空数组会拿方名为key
 * @param expiration 过期时间(秒);默认60秒
 */
function MyDebounce({
  key,
  expiration,
  error,
}: {
  key: { paramIndex: number; fields: string[] }[] | string | (() => string);
  expiration?: number;
  error?: { code: string; message: string };
}) {
  return function (target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    descriptor.value = async function (...args) {
      let values = '';
      //string作为key
      if (_.isString(key)) {
        values = key;
      }
      //函数作为key
      if (_.isFunction(key)) {
        values = key();
      }
      //方法和入参作为key
      if (_.isArray(key)) {
        key?.map((res) => {
          const params = args[res.paramIndex];
          if (!_.isArray(res.fields) || _.isEmpty(res.fields)) {
            values += JSON.stringify(params);
          } else {
            const map = {};
            res.fields.map((field) => {
              if (undefined != params[field]) map[field] = params[field];
            });
            values += !_.isEmpty(map)
              ? JSON.stringify(map)
              : JSON.stringify(params);
          }
        });
      }
      // 获取类名
      const className = target.constructor.name;
      const lockKey = values ? `${className}:${propertyKey}:${values}` : `${className}:${propertyKey}`;
      console.log(`lockKey: ${lockKey}`);
      const lockValue = Date.now().toString();
      // 尝试获取锁
      const lockAcquired = await MyRedis.nxset(
        lockKey,
        lockValue,
        expiration || 60
      );
      if (lockAcquired !== 'success') {
        if (!error) return;
        throw new YqzException({
          code: error?.code || '202',
          msg: error?.message || 'Please try again later',
        });
      }
      try {
        // 执行原始方法
        return await originalMethod.apply(this, args);
      } finally {
        // 释放锁
        await MyRedis.clear(lockKey);
      }
    };
    return descriptor;
  };
}
