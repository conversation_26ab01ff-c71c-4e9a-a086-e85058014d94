import { Cron, CronOptions } from '@nestjs/schedule';

function MyCron(
  cronTime: string | Date,
  options?: CronOptions
): MethodDecorator {
  if (process.env.YQZ_DISABLE_CRON === 'true') {
    console.log(`MyCron disabled`);
    return BaseMehodDecorator();
  }
  return Cron(cronTime, options);
}

// private methods
function BaseMehodDecorator(): MethodDecorator {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor
  ) {
    return;
  };
}

export { MyCron };
