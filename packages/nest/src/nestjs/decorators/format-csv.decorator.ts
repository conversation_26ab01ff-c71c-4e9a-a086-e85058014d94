import { MyLogger } from '../modules';
import { CsvUtil } from '../utils';
import { PropertyCsv } from '../utils/types/util.type';

const logger = new MyLogger('CheckFormatCsv');
function CheckFormatCsv(config: PropertyCsv[] | ((param: any) => PropertyCsv[]), name?: string): MethodDecorator {
  return function (
    target: any,
    key: string | symbol,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // 获取方法的入参
      const [formatValue] = args;
      // 获取方法的返回值
      const result = await originalMethod.apply(this, args);
      // 默认格式化字段
      const list = name ? result[name] : result?.items;
      // 检查是否入参中包含字段 'format'，且值为 'csv'
      if (formatValue && formatValue.format === 'csv' && Array.isArray(list)) {
        // 动态生成列配置
        const dynamicConfig = typeof config === 'function' ? config(formatValue) : config;
        // 提取字段 'items' 并映射返回数组
        const csv = CsvUtil.mapObjectPropertiesToChinese(list, dynamicConfig);
        return CsvUtil.convert2Csv(csv);
      }

      // 调用原始方法并返回结果
      return result;
    };

    return descriptor;
  };
}

export { CheckFormatCsv };
