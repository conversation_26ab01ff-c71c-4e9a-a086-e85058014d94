import { SetMetadata } from '@nestjs/common';
import type { Request } from 'express';

export const Key = 'auth:require-auth';

type Dept = {
  id: string;
  name?: string;
};

export type AuthScope<T = any> = {
  scope: {
    _level?: 'company' | 'department' | 'subject';
    company?: {
      id: string;
    };
    dept?: Dept;
    subject?: T & CommonSubjectRole;
  };
};

type CommonSubjectRole = {
  created_by: string;
};

type RequireAuthEle = {
  action: string;
  subjectType: string;
  idGetter?: string | ((req: Request) => string);
  dataGetter?: (req: Request) => Partial<AuthScope> & { projectId?: string };
  handling?: 'default' | ((pass: boolean, req: Request) => void);
};
export type RequireAuthParams = RequireAuthEle[];
export const Set = (params: RequireAuthParams) => SetMetadata(Key, params);
