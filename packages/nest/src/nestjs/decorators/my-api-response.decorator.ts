import { applyDecorators, Type } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';
import { ApiBaseRes } from '../../types';

// 参考文章
// https://aalonso.dev/blog/how-to-generate-generics-dtos-with-nestjsswagger-422g

export const MyDefaultApiOkResponse = () =>
  applyDecorators(
    ApiExtraModels(ApiBaseRes),
    ApiOkResponse({
      schema: {
        allOf: [{ $ref: getSchemaPath(ApiBaseRes) }],
      },
    })
  );

export const MyApiOkResponse = <DataDto extends Type<unknown>>(
  dataDto: DataDto
) =>
  applyDecorators(
    ApiExtraModels(ApiBaseRes, dataDto),
    ApiOkResponse({
      schema: {
        allOf: [
          { $ref: getSchemaPath(ApiBaseRes) },
          {
            properties: {
              data: { $ref: getSchemaPath(dataDto) },
            },
          },
        ],
      },
    })
  );

export const MyApiOkArrayResponse = <DataDto extends Type<unknown>>(
  dataDto: DataDto
) =>
  applyDecorators(
    ApiExtraModels(ApiBaseRes, dataDto),
    ApiOkResponse({
      schema: {
        allOf: [
          { $ref: getSchemaPath(ApiBaseRes) },
          {
            properties: {
              data: {
                type: 'array',
                items: { $ref: getSchemaPath(dataDto) },
              },
            },
          },
        ],
      },
    })
  );
