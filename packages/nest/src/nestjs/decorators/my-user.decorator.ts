import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { JwtUserGw, JwtUserV22 } from '../../types';
import { USER_GW__KEY, USER_KEY } from '../middlewares';

/**
 * userVersion: default "gw"
 */
export const MyUser = createParamDecorator(
  (userVersion: 'v2' | 'gw' = 'gw', ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    if (userVersion === 'gw') return request[USER_GW__KEY] as JwtUserGw;
    return request[USER_KEY] as JwtUserV22;
  }
);
