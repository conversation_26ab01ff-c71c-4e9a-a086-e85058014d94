import { Timeout } from '@nestjs/schedule';

function DevTimeout(timeout: number): MethodDecorator {
  try {
    if (process.env.ALLOW_DEV_TIMOUT === 'true') {
      return Timeout(timeout);
    }
    return BaseMehodDecorator();
  } catch (err) {
    return BaseMehodDecorator();
  }
}

function BaseMehodDecorator(): MethodDecorator {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor
  ) {
    return;
  };
}

export { DevTimeout };
