import {
  RabbitHandlerConfig,
  RabbitSubscribe,
} from '@golevelup/nestjs-rabbitmq';
type OriginConfig = Pick<
  RabbitHandlerConfig,
  | 'exchange'
  | 'routingKey'
  | 'allowNonJsonMessages'
  | 'queue'
  | 'queueOptions'
  | 'errorBehavior'
  | 'errorHandler'
>;

export function MyRabbitsubscribe(
  config: OriginConfig,
  activate = true
): MethodDecorator {
  return activate ? RabbitSubscribe(config) : BaseMehodDecorator();
}

// private methods
function BaseMehodDecorator(): MethodDecorator {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor
  ) {
    return;
  };
}
