import { isNull, isUndefined } from 'lodash';
import { MyLogger } from '../modules';
import { MyRedis } from '../../redis/my-redis';
import { CryptoUtiil } from '../utils/crypto.util';
const logger = new MyLogger('MyCache');

export { MyCache };

/**
 * usage
 * @MyCache({cacheKey:'my-cache-key', ttl:10})
 * @MyCache('my-cache-key') //default ttl=60 seconds
 */
// eslint-disable-next-line sonarjs/cognitive-complexity
const MyCache = (
  params: { cacheKey: string; ttl?: number; exclude?: string[] } | string
): MethodDecorator => {
  // eslint-disable-next-line sonarjs/cognitive-complexity
  if (!process.env.YQZ_SERVICE_NAME)
    throw new Error('[MyCache] missing env YQZ_SERVICE_NAME');
  const PREFIX = process.env.YQZ_SERVICE_NAME.toLowerCase();

  const DEFAULT_TTL = 60; // in seconds
  let cacheKey,
    ttl = DEFAULT_TTL,
    exclude: string[];

  if (typeof params === 'string') {
    cacheKey = params;
  } else if (typeof params === 'object') {
    cacheKey = params.cacheKey;
    ttl = params.ttl || DEFAULT_TTL;
    exclude = [
      ...(params.exclude || []),
      't',
      'sign',
      'pl_build',
      'pl_user',
      'pl_personId',
      'pl_company',
    ];
  }

  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor
  ) => {
    const originalMethod = descriptor.value;
    descriptor.value = function (...args: any[]) {
      return new Promise((resolve, reject) => {
        // 组装key
        const argsKey = args
          .filter((e) => !!e)
          .map((arg) => {
            const _arg = Object.assign({}, arg);
            if (exclude?.length > 0 && typeof arg === 'object')
              exclude.forEach((e) => delete _arg[e]);

            try {
              return JSON.stringify(_arg);
            } catch {
              return '_';
            }
          })
          .join(':');

        // args.forEach((arg: any) => {
        //   if (exclude?.length > 0 && arg && typeof arg === 'object') {
        //     const _arg = Object.assign({}, arg);
        //     exclude.forEach((e) => delete _arg[e]);
        //     composedCacheKey += ':' + (_arg ? JSON.stringify(_arg) : '');
        //   } else composedCacheKey += ':' + (arg ? JSON.stringify(arg) : '');
        // });

        const composedCacheKey = CryptoUtiil.generateMD5(
          `${PREFIX}:${cacheKey}:${argsKey}`
        );

        MyRedis.get(composedCacheKey)
          .then((res) => {
            // 有cache,则返回cache
            if (res) {
              // logger.log(`hitting cache key: `, {
              //   composedCacheKey,
              //   args,
              //   res,
              // });
              const result = JSON.parse(res);
              if (EmptyValues.includes(result)) {
                resolve(parseEmptyString(result));
                return;
              }
              resolve(result);
              return;
            }

            // 没有redis缓存数据，则原始数据源获取
            originalMethod
              .apply(this, args)
              .then((res) => {
                processRes({ res, resolve, ttl, composedCacheKey });
              })
              .catch((error) => {
                reject(error);
              });
          })
          .catch((err) => {
            // 查询缓存出错，则也从原始数据源获取
            logger.error(err.message || err);
            originalMethod
              .apply(this, args)
              .then((res) => {
                processRes({ res, resolve, ttl, composedCacheKey });
              })
              .catch((error) => {
                reject(error);
              });
          });
      });
    };
  };
};

function processRes({
  res,
  ttl,
  composedCacheKey,
  resolve,
}: {
  res: any;
  ttl: number;
  composedCacheKey: string;
  resolve: (data: any) => void;
}) {
  let cache = res;
  if (!res) {
    const emptySerilizable = convertempty2string(res);
    if (emptySerilizable) cache = emptySerilizable;
  }

  MyRedis.set(composedCacheKey, JSON.stringify(cache), ttl).catch((err) => {
    logger.error(err.message);
  });

  resolve(res);
}

function convertempty2string(value: any): EmptyValue {
  if (isNull(value)) return '__null';
  if (isUndefined(value)) return '__undefined';
  if (isNaN(value)) return '__nan';
  if (value === '') return '__';
  return value;
}

type EmptyValue = '__null' | '__undefined' | '__nan' | '__';
const EmptyValues: EmptyValue[] = ['__null', '__undefined', '__nan', '__'];
function parseEmptyString(value: EmptyValue) {
  const map: { [keys in EmptyValue]: any } = {
    __null: null,
    __undefined: undefined,
    __nan: NaN,
    __: '',
  };
  return map[value];
}
