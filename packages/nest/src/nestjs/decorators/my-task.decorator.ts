import { Cron } from '@nestjs/schedule';
import * as DateFns from 'date-fns';
import { SmartOpsApi } from '../../api';
import { MyRedis } from '../../redis';
import { Task } from '../../types';
import { MyLogger } from '../modules';
import * as uuid from 'uuid';
import { SleepUtil } from '../utils';
import { trace } from '@opentelemetry/api';

const logger = new MyLogger('MyTask');
const DEFAULT_TTL_SECONDS = 10 * 60; // in seconds

/**
 * 开发环境设置环境变量 YQZ_DISABLE_MY_TASK=Y,可以禁用任务的注册和启动
 * @param task_id: string;
 * @param task_desc: string;
 * @param start_cron: string;
 * @param end_cron?: string;   // 定时检查任务运行结果
 * @param ttl_seconds?: number; // 默认10分钟
 * @param activated?: boolean  //默认true
 * @returns
 */
function MyTask(config: Task.Config): MethodDecorator {
  // 本地开发设置YQZ_DISABLE_MY_TASK,可以禁用MyTask
  if (process.env.YQZ_DISABLE_MY_TASK === 'Y') {
    console.log(`MyTask disabled`);
    return BaseMehodDecorator();
  }

  // disabled = false
  const activated =
    typeof config.activated === 'boolean' ? config.activated : true;
  if (!activated) {
    console.log(`MyTask disabled (activate=false)`);
    return BaseMehodDecorator();
  }

  config.task_id = config.task_id.trim();
  config.task_desc = config.task_desc?.trim();
  config.start_cron = config.start_cron?.trim();
  config.end_cron = config.end_cron?.trim();
  0;
  return (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor
  ) => {
    // 更新或者保存任务定义
    SmartOpsApi.saveTask({
      task_id: config.task_id,
      task_desc: config.task_desc,
      start_cron: config.start_cron,
      end_cron: config.end_cron,
      service: process.env.YQZ_SERVICE_NAME || '',
    }).then((res) => {
      logger.log(`注册任务 ${config.task_id}: ${res.msg}`);
    });

    // step1: 修改方法，用startTask启动
    const originalMethod = descriptor.value;
    descriptor.value = function (...args: any[]) {
      return startTask({
        config,
        task: () => {
          return originalMethod.apply(this, ...args);
        },
      });
    };
    // step2: 用cron调度执行
    Cron(config.start_cron)(target, propertyKey, descriptor);
  };
}

async function startTask({
  task,
  config,
}: {
  task: () => Promise<any>;
  config: Task.Config;
}) {
  let res = '';
  const { task_id } = config;
  const key = `locktask:${task_id}`;
  const count = await MyRedis.incr(key);
  logger.log(`task ${task_id} incr count ${count}`);
  if (count > 1) {
    res = `task ${task_id} is still running, try again later`;
    logger.warn(res);
    return res;
  }

  await MyRedis.expire(key, config.ttl_seconds || DEFAULT_TTL_SECONDS);

  const uid = uuid.v4().replace(/-/g, '');
  const tracer = trace.getTracer('my-task');
  // 用 OpenTelemetry span 包裹定时任务逻辑
  return await tracer.startActiveSpan(
    `cronjob:${task_id}`,
    async (span) => {
      try {
        // 汇报任务开始执行
        logger.log(`task ${task_id} started`);
        await SmartOpsApi.reportTaskStatus({
          task_id,
          uid,
          status: Task.Status.Running,
        });

        // 执行任务
        res = await task();

        // 汇报任务开始完成
        logger.log(`task ${task_id} finished`);
        await SmartOpsApi.reportTaskStatus({
          task_id,
          uid,
          status: Task.Status.Success,
        });
        span.setStatus({ code: 1 }); // OK
      } catch (err) {
        logger.error(err);
        const errMsg = (err && typeof err === 'object' && 'message' in err) ? (err as any).message : (err as any)?.toString?.() || 'unknown';
        res = `task ${task_id} failed: ${errMsg} (${DateFns.format(new Date(), 'M月d日HH:mm:ss')})`;
        logger.log(res);

        // 汇报任务失败
        await SmartOpsApi.reportTaskStatus({
          task_id,
          uid,
          status: Task.Status.Failure,
          content: res,
        });
        span.setStatus({ code: 2, message: errMsg }); // ERROR
      } finally {
        span.end();
        // sleep三秒后再释放（防止任务执行太快，重复跑定时任务）
        await SleepUtil.sleep(1000 * 3);
        logger.log(`task ${task_id} release lock`);
        // 释放任务锁
        await MyRedis.expire(key, 0);
      }
      return res;
    }
  );
}

export { MyTask };

function BaseMehodDecorator(): MethodDecorator {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor
  ) {
    return;
  };
}
