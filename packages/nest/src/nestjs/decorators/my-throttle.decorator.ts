import * as _ from 'lodash';
import { SetMetadata } from '@nestjs/common';

export namespace MyThrottle {
    export interface Params {
        key: { paramIndex: number; fields: string[] }[] | string | (() => string);
        limit: number;
        ttl: number;
        error?: { code: string; message: string };
    }
    export const Key = 'throttle:config';
    export const Set = (params: Partial<Params>) => SetMetadata(Key, {
        key: params.key,
        limit: params.limit ?? 60,
        ttl: params.ttl ?? 60,
        error: params.error
    });
}