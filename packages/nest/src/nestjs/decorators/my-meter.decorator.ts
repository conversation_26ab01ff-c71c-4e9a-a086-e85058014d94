import { MyLogger } from '../modules/logger/my-logger.service';

export { MyMeter };
const logger = new MyLogger('\tMyMeter');

const MyMeter = (
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
) => {
  const className = target.constructor?.name;
  const originalMethod = descriptor.value;
  descriptor.value = function (...args: any) {
    const start = new Date().getTime();
    const ret = originalMethod.apply(this, args);
    if (ret instanceof Promise) {
      return new Promise((resolve, reject) => {
        ret
          .then((res) => {
            resolve(res);
            const end = new Date().getTime();
            logger.log(`${className}.${propertyKey} toke ${end - start} ms`);
          })
          .catch((error) => {
            reject(error);
          });
      });
    } else {
      const end = new Date().getTime();
      logger.log(`${className}.${propertyKey} toke ${end - start} ms`);
      return ret;
    }
  };
};
