import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import type { Request } from 'express';
import { USER_KEY } from '../middlewares';
import { JwtUserV22 } from '../../types';
import { YqzException } from '../filters';

type MyParam =
  | 'managerId'
  | 'companyId'
  | 'roleList'
  | string
  | ((params: { request: Request; user: JwtUserV22 }) => void);
export const InjectUserIntoReq = createParamDecorator(
  (data: MyParam[], ctx: ExecutionContext) => {
    const request: Request = ctx.switchToHttp().getRequest();
    if (data?.length > 0) {
      const body = request.body;
      const query = request.query;

      const user: JwtUserV22 = request[USER_KEY];
      // 内部服务不做转化
      if (user?.userType === 'yqz-service') return;

      if (user?.userType !== 'worker')
        throw new YqzException({
          code: '202',
          msg: '您没有相应的操作权限(202308170843)',
        });

      for (const ele of data) {
        if (typeof ele === 'string') {
          const [fromFieldName, toFieldName] = ele.split(':');
          if ('companyId' === fromFieldName) {
            const value = user.targetCompany?.companyId;
            body[toFieldName || fromFieldName] = value;
            query[toFieldName || fromFieldName] = value;
          }

          if ('managerId' === fromFieldName) {
            const value = user.targetCompany?.managerId;
            body[toFieldName || fromFieldName] = value;
            query[toFieldName || fromFieldName] = value;
          }

          if ('roleList' === fromFieldName) {
            const value = user.targetCompany?.roleList;
            body[toFieldName || fromFieldName] = value;
            query[toFieldName || fromFieldName] = value;
          }
        } else if (typeof ele === 'function') {
          ele({ request, user });
        }
      }
    }

    // Returning the modified request object is optional
    // You can return any value or data you want to inject to your route handler
    return request;
  }
);
