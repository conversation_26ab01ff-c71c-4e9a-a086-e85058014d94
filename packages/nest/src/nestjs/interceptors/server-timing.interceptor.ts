import { 
  Injectable, 
  NestInterceptor, 
  ExecutionContext, 
  CallHandler 
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Response } from 'express';
import { isRabbitContext } from '@golevelup/nestjs-rabbitmq';

@Injectable()
export class ServerTimingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // 过滤mq请求
    if (isRabbitContext(context)) {
      return next.handle();
    }

    const ctx = context.switchToHttp();
    const response = ctx.getResponse<Response>();
    const requestStart = Date.now();

    // 暴露 Server-Timing 头
    response.setHeader('Access-Control-Expose-Headers', 'Server-Timing');
    
    return next.handle().pipe(
      tap(() => {
        // 在响应发送前计算时间并设置header
        const duration = Date.now() - requestStart;
        const timingValue = `agile;dur=${duration}`;
        
        // 获取现有的 Server-Timing 头
        const existingServerTiming = response.getHeader('Server-Timing');
        
        // 如果已存在，则追加；否则设置新的
        if (existingServerTiming) {
          response.setHeader('Server-Timing', `${existingServerTiming}, ${timingValue}`);
        } else {
          response.setHeader('Server-Timing', timingValue);
        }        
        // 设置测试头用于调试
        response.setHeader('X-Yqz-Test', '1');
                
      })
    );
  }
}