import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import * as uuid from 'uuid';
import { isRabbitContext } from '@golevelup/nestjs-rabbitmq';

export interface Response<T> {
  data: T;
}

export class TransformInterceptor<T>
  implements NestInterceptor<T, Response<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    if (isRabbitContext(context)) return next.handle();
    return next.handle().pipe(
      map((data) => {
        // 如果返回的是 Buffer，说明是文件流，直接返回
        if (data instanceof Buffer) return data;
        // 否则，正常封装 JSON
        return {
          data,
          code: '200',
          msg: '请求成功',
          rid: uuid.v4().replace(/-/g, ''),
        };
      })
    );
  }
}
