import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import * as _ from 'lodash';
import * as uuid from 'uuid';
import { isRabbitContext } from '@golevelup/nestjs-rabbitmq';
import { MyLogger } from '../modules';
import { ReqHeader } from '../../types';
import { Request } from 'express';

/**
 * 日志拦截器
 */
const logger = new MyLogger('LoggingInterceptor');
export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    //rabbitMq日志
    if (isRabbitContext(context)) {
      const message = context.getArgs()[0];
      const routingKey = context.getArgs()[1].fields.routingKey;
      const exchange = context.getArgs()[1].fields.exchange;
      logger.log(`MqInbound: ${exchange} ${routingKey} ${JSON.stringify(message)}`);
      return next.handle();
    }

    const request: Request = context.switchToHttp().getRequest();
    if (!request.headers) return next.handle();

    const excludePaths = ['/', '/health'];
    if (excludePaths.includes(request.url)) return next.handle();

    const key = request.headers[ReqHeader.YqzReqId] || uuid.v4().replace(/-/g, '');
    const start = new Date().getTime();
    const user = getUser(request.headers);

    let fileLog = '';
    // 获取请求头中的 Content-Length
    const contentLength = request?.headers['content-length'];
    if (contentLength) fileLog += `${contentLength} 字节`;

    //请求日志
    logger.log(key, request.method, request.url, 'user:', user, 'body:', _.isEmpty(request.body) ? 'null' : JSON.stringify(request.body), fileLog ? `Content-Length: ${fileLog}` : '');
    
    //响应日志
    return next.handle().pipe(
      map((data) => {
        logger.log(key, `${new Date().getTime() - start}ms`, request.method, request.url, 'response:', JSON.stringify(data));
        return data;
      })
    );
  }
}

function getUser(headers: any) {
  return headers[ReqHeader.YqzUser] || headers[ReqHeader.JwtPayload] || 'anonymous';
}
