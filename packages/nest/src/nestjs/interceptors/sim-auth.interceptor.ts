import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { tap } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { isRabbitContext } from '@golevelup/nestjs-rabbitmq';
import { AuthPassKey, isSimAuth } from '../guards';

/**
 * 业务接口做权限检查拦截器
 */
@Injectable()
export class SimAuthInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    if (isRabbitContext(context)) { return next.handle(); }
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    if (isSimAuth(request)) {//是否是业务接口做权限检查
      response.status(200).json({
        code: '200',
        data: {
          pass: request[AuthPassKey] || 0, //0表示没有权限检查
        },
      });
      return of(null); // Ends the request without proceeding to the route handler
    }

    return next.handle().pipe(
      tap(() => {
        // Additional logic after the request if needed
      })
    );
  }

}