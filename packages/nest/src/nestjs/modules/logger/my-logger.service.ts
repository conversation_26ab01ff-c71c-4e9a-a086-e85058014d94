import { LoggerService } from '@nestjs/common';
import * as uuid from 'uuid';
import { context as otelContext, trace as otelTrace } from '@opentelemetry/api';
export class MyLogger implements LoggerService {
  private context = '';
  private subConexts: string[] = [];
  private showContext = true;

  constructor(context?: string) {
    console.log('init logger', context || 'App');
    this.context = context || '';
  }

  prefix(subContext: string) {
    this.subConexts.push(subContext);
    return this;
  }

  uuid() {
    this.subConexts.push(uuid.v4().replace(/-/g, ''));
    return this;
  }

  clearPrefix() {
    this.subConexts = [];
    return this;
  }

  popPrefix() {
    this.subConexts.pop();
    return this;
  }

  echo() {
    if (this.subConexts.length > 0) {
      console.log(this.prefixes);
      return this;
    }

    try {
      const err = new Error();
      let caller = '';
      for (const line of (err.stack || '').split('\n').slice(2)) {
        if (line.includes('anonymous')) continue;

        const matches = /at (\w+\.\w+)/.exec(line);
        if (!matches) continue;

        caller = matches[1];
        const callerList = caller.split('.');

        // special cases
        if (callerList[0] === 'OperatorSubscriber') continue;
        if (callerList[0] === 'Object') {
          const fileName = (/\/(\w+\.\w+\.*\w*):\d+:\d+/.exec(line) || [])[1];
          if (fileName) callerList[0] = fileName;
        }

        const funcName = `${callerList.join(' ')} :`.replace(
          / callFunction/,
          ''
        );
        console.log(funcName);
        return this;
      }

      const line = (err.stack || '').split('\n')[2];
      if (/at (\w+).*\.ts:\d+:\d+/.test(line)) {
        const fileName = (/\/((\w|\.|-|_)+:\d+:\d)+/.exec(line) || [])[1];
        console.log(fileName);
      } else {
        this.prefixes ? console.log(this.prefixes) : console.log();
      }
    } catch (err) {
      this.prefixes ? console.log(this.prefixes) : console.log();
    } finally {
      return this;
    }
  }

  log(...args: any[]) {
    // 获取当前活跃 span
    const span = otelTrace.getSpan(otelContext.active());
    const traceId = span?.spanContext().traceId;
    const spanId = span?.spanContext().spanId;

    const tracePrefix = traceId && spanId ? `[traceId=${traceId}] [spanId=${spanId}]` : '';

    if (!this.showContext) {
      console.log(tracePrefix, ...args);
      return this;
    }

    if (this.subConexts.length > 0) {
      console.log(tracePrefix, this.prefixes, ':', ...args);
      return this;
    }

    try {
      const err = new Error();
      let caller = '';
      for (const line of (err.stack || '').split('\n').slice(2)) {
        if (line.includes('anonymous')) continue;

        const matches = /at (\w+\.\w+)/.exec(line);
        if (!matches) continue;

        caller = matches[1];
        const callerList = caller.split('.');

        // special cases
        if (callerList[0] === 'OperatorSubscriber') continue;
        if (callerList[0] === 'Object') {
          const fileName = (/\/(\w+\.\w+\.*\w*):\d+:\d+/.exec(line) || [])[1];
          if (fileName) callerList[0] = fileName;
        }

        const funcName = `${callerList.join(' ')} :`.replace(
          / callFunction/,
          ''
        );
        console.log(tracePrefix, funcName, ...args);
        return this;
      }

      const line = (err.stack || '').split('\n')[2];
      if (/at (\w+).*\.ts:\d+:\d+/.test(line)) {
        const fileName = (/\/((\w|\.|-|_)+:\d+:\d)+/.exec(line) || [])[1];
        console.log(tracePrefix, fileName, ...args);
      } else {
        this.prefixes
          ? console.log(tracePrefix, this.prefixes, ...args)
          : console.log(tracePrefix, ...args);
      }
    } catch (err) {
      this.prefixes
        ? console.log(tracePrefix, this.prefixes, ...args)
        : console.log(tracePrefix, ...args);
    } finally {
      return this;
    }
  }

  error(...args: any[]) {
    // 获取当前活跃 span
    const span = otelTrace.getSpan(otelContext.active());
    const traceId = span?.spanContext().traceId;
    const spanId = span?.spanContext().spanId;
    const tracePrefix = traceId && spanId ? `[traceId=${traceId}] [spanId=${spanId}]` : '';

    if (!this.showContext) {
      console.error(tracePrefix, ...args);
      return this;
    }

    if (this.subConexts.length > 0) {
      console.error(tracePrefix, this.prefixes, ':', ...args);
      return this;
    }

    try {
      const err = new Error();
      let caller = '';
      for (const line of (err.stack || '').split('\n').slice(2)) {
        if (line.includes('anonymous')) continue;

        const matches = /at (\w+\.\w+)/.exec(line);
        if (!matches) continue;

        caller = matches[1];
        const callerList = caller.split('.');
        // special cases
        if (callerList[0] === 'OperatorSubscriber') continue;
        if (callerList[0] === 'Object') {
          const fileName = (/\/(\w+\.\w+\.*\w*):\d+:\d+/.exec(line) || [])[1];
          if (fileName) callerList[0] = fileName;
        }
        const funcName = `${callerList.join(' ')} :`.replace(
          / callFunction/,
          ''
        );
        console.error(tracePrefix, funcName, ...args);
        return this;
      }

      const line = (err.stack || '').split('\n')[2];
      if (/at (\w+).*\.ts:\d+:\d+/.test(line)) {
        const fileName = (/\/((\w|\.|-|_)+:\d+:\d)+/.exec(line) || [])[1];
        console.error(tracePrefix, fileName, ...args);
      } else {
        this.prefixes
          ? console.error(tracePrefix, this.prefixes, ...args)
          : console.error(tracePrefix, ...args);
      }
    } catch (err) {
      this.prefixes
        ? console.error(tracePrefix, this.prefixes, ...args)
        : console.error(tracePrefix, ...args);
    } finally {
      return this;
    }
  }

  warn(...args: any[]) {
    // 获取当前活跃 span
    const span = otelTrace.getSpan(otelContext.active());
    const traceId = span?.spanContext().traceId;
    const spanId = span?.spanContext().spanId;
    const tracePrefix = traceId && spanId ? `[traceId=${traceId}] [spanId=${spanId}]` : '';

    if (!this.showContext) {
      console.warn(tracePrefix, ...args);
      return this;
    }

    if (this.subConexts.length > 0) {
      console.warn(tracePrefix, this.prefixes, ':', ...args);
      return this;
    }

    try {
      const err = new Error();
      let caller = '';
      for (const line of (err.stack || '').split('\n').slice(2)) {
        if (line.includes('anonymous')) continue;

        const matches = /at (\w+\.\w+)/.exec(line);
        if (!matches) continue;

        caller = matches[1];
        const callerList = caller.split('.');
        // special cases
        if (callerList[0] === 'OperatorSubscriber') continue;
        if (callerList[0] === 'Object') {
          const fileName = (/\/(\w+\.\w+\.*\w*):\d+:\d+/.exec(line) || [])[1];
          if (fileName) callerList[0] = fileName;
        }
        const funcName = `${callerList.join(' ')} :`.replace(
          / callFunction/,
          ''
        );
        console.warn(tracePrefix, funcName, ...args);
        return this;
      }

      const line = (err.stack || '').split('\n')[2];
      if (/at (\w+).*\.ts:\d+:\d+/.test(line)) {
        const fileName = (/\/((\w|\.|-|_)+:\d+:\d)+/.exec(line) || [])[1];
        console.warn(tracePrefix, fileName, ...args);
      } else {
        this.prefixes
          ? console.warn(tracePrefix, this.prefixes, ...args)
          : console.warn(tracePrefix, ...args);
      }
    } catch (err) {
      this.prefixes
        ? console.warn(tracePrefix, this.prefixes, ...args)
        : console.warn(tracePrefix, ...args);
    } finally {
      return this;
    }
  }

  disableContext() {
    this.showContext = false;
  }
  enableContext() {
    this.showContext = true;
  }

  get prefixes() {
    if (this.subConexts.length === 0) return this.context;
    return [this.context, ...this.subConexts].join(' ');
  }
}
