import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { Response } from 'express';
import * as _ from 'lodash';
import * as uuid from 'uuid';
import { MyLogger } from '../modules';

export enum YqzErrorCode {
  DEFAULT_CODE = '202',
}

export const YqzError = {
  MissingReqParams: {
    code: '201',
    msg: '缺少必要参数',
  },
};

export class YqzException extends Error {
  code: string | number;//兼容老版本number类型
  msg: string;
  yqzErrorCode?: string; // deprecated 向下兼容
  yqzErrorMessage?: string; // deprecated 向下兼容
  constructor(
    params: { code: string | number; msg: string } | string,
    customMsg?: string,
    serviceName?: string
  ) {
    if (typeof params === 'string') {
      const newMsg = generateMsg(params, serviceName);
      super(newMsg);
      this.msg = newMsg;
      this.code = YqzErrorCode.DEFAULT_CODE;
    }

    if (typeof params === 'object') {
      const newMsg = generateMsg(params.msg, serviceName);
      super(newMsg);
      this.msg = newMsg;
      this.code = params.code || YqzErrorCode.DEFAULT_CODE;
    }

    function generateMsg(message: string, name?: string) {
      const msgArr: string[] = [];
      if ((!name || 'Y' == name) && process.env.YQZ_SERVICE_NAME)
        msgArr.push(`[${process.env.YQZ_SERVICE_NAME}]`);
      if (message) msgArr.push(message);
      if (customMsg) msgArr.push(customMsg);
      return msgArr.join(' ');
    }
  }
}

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private logger: MyLogger = new MyLogger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    const rid = uuid.v4().replace(/-/g, '');

    if (!(exception instanceof NotFoundException))
      this.logger.error({ rid, exception });

    if (exception instanceof YqzException) {
      response.status(200).json({
        code: exception.code || exception.yqzErrorCode,
        msg: exception.msg || exception.yqzErrorMessage,
        rid,
      });
      return;
    }

    //class validation error
    if (exception instanceof BadRequestException) {
      const messages: string[] = (exception.getResponse() as any).message;
      if (_.isArray(messages)) {
        response.status(200).json({
          code: YqzErrorCode.DEFAULT_CODE,
          error_msg: `${process.env.YQZ_SERVICE_NAME} error: ${messages.join(
            ', '
          )}`,
          rid,
        });
        return;
      }
    }

    if (exception instanceof Error) {
      response.status(200).json({
        code: YqzErrorCode.DEFAULT_CODE,
        msg: `${process.env.YQZ_SERVICE_NAME || ''} error: ${
          exception.message
        }`,
        rid,
      });
      return;
    }

    response.status(200).json({
      code: YqzErrorCode.DEFAULT_CODE,
      msg: (exception as any).message || '系统异常',
      rid,
    });
  }
}
