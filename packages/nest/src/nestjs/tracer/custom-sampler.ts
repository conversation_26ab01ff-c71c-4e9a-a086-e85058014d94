import { Sampler, <PERSON>plingDecision, SamplingResult, TraceIdRatioBasedSampler } from '@opentelemetry/sdk-trace-base';
import { Context, SpanKind } from '@opentelemetry/api';

/**
 * 增强采样器 - 支持基于性能和异常的智能采样
 *
 * 采样策略：
 * 1. 超过1s的HTTP调用或远程调用的整体链路强制采样
 * 2. 异常的HTTP调用或远程调用的整体链路强制采样
 * 3. 其他请求使用配置的采样率
 */
export class EnhancedSampler implements Sampler {
    private ratioSampler: TraceIdRatioBasedSampler;
    private slowCallThreshold: number;
    private forceSampledTraces: Set<string> = new Set();

    constructor() {
        // 使用环境变量设置采样率，默认为0.1
        this.ratioSampler = new TraceIdRatioBasedSampler(Number(process.env.TRACER_SAMPLER_RATIO || 0.1));
        // 慢调用阈值，默认1000ms
        this.slowCallThreshold = Number(process.env.TRACER_SLOW_CALL_THRESHOLD || 1000);
    }

    /**
     * 标记trace需要强制采样（用于慢调用和异常调用）
     */
    markTraceForForcedSampling(traceId: string): void {
        this.forceSampledTraces.add(traceId);
        // 清理过期的trace ID，避免内存泄漏
        if (this.forceSampledTraces.size > 10000) {
            const toDelete = Array.from(this.forceSampledTraces).slice(0, 5000);
            toDelete.forEach(id => this.forceSampledTraces.delete(id));
        }
    }

    /**
     * 检查是否为HTTP或远程调用相关的span
     */
    private isHttpOrRemoteCall(spanName: string, spanKind?: SpanKind, attributes?: any): boolean {
        // 检查span类型
        if (spanKind === SpanKind.CLIENT || spanKind === SpanKind.SERVER) {
            return true;
        }

        // 检查span名称
        if (spanName.includes('HTTP') || spanName.includes('http') ||
            spanName.includes('GET') || spanName.includes('POST') ||
            spanName.includes('PUT') || spanName.includes('DELETE') ||
            spanName.includes('PATCH')) {
            return true;
        }

        // 检查属性
        if (attributes) {
            return !!(attributes['http.method'] ||
                attributes['http.url'] ||
                attributes['http.target'] ||
                attributes['rpc.service'] ||
                attributes['db.operation']);
        }

        return false;
    }

    shouldSample(
        context: Context,
        traceId: string,
        spanName: string,
        spanKind?: SpanKind,
        attributes?: any,
        links?: any
    ): SamplingResult {
        // 过滤 http 层的 GET /health
        if ((spanName === 'GET' || spanName === 'POST') && attributes &&
            (attributes['http.target'] === '/health' || attributes['http.route'] === '/health')) {
            return { decision: SamplingDecision.NOT_RECORD };
        }

        // 兼容 handler 层
        if (spanName.includes('/health')) {
            return { decision: SamplingDecision.NOT_RECORD };
        }

        // 过滤掉环境变量获取
        if (spanName.startsWith('middleware - ') || spanName.startsWith('getEnvMiddleware')) {
            return { decision: SamplingDecision.NOT_RECORD };
        }

        // 如果这个trace已经被标记为强制采样，则采样
        if (this.forceSampledTraces.has(traceId)) {
            return { decision: SamplingDecision.RECORD_AND_SAMPLED };
        }

        // 对于HTTP或远程调用，我们需要特殊处理
        // 注意：在shouldSample阶段我们还不知道执行时间和异常状态
        // 这些信息需要通过SpanProcessor在span结束时处理

        // 进行正常采样(默认采样率0.1)
        return this.ratioSampler.shouldSample(context, traceId);
    }

    toString(): string {
        return 'EnhancedSampler';
    }
}

// 保持向后兼容
export class CustomSampler extends EnhancedSampler {
    toString(): string {
        return 'CustomSampler';
    }
}