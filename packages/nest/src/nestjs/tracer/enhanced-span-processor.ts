import { SpanProcessor, ReadableSpan } from '@opentelemetry/sdk-trace-base';
import { Context, SpanKind, SpanStatusCode } from '@opentelemetry/api';
import { EnhancedSampler } from './custom-sampler';

/**
 * 增强Span处理器 - 监听span生命周期，实现基于性能和异常的动态采样
 * 
 * 功能：
 * 1. 监听span结束事件
 * 2. 检测慢调用（超过阈值的HTTP/远程调用）
 * 3. 检测异常调用
 * 4. 通知采样器进行强制采样
 */
export class EnhancedSpanProcessor implements SpanProcessor {
    private sampler: EnhancedSampler;
    private slowCallThreshold: number;

    constructor(sampler: EnhancedSampler) {
        this.sampler = sampler;
        // 慢调用阈值，默认1000ms
        this.slowCallThreshold = Number(process.env.TRACER_SLOW_CALL_THRESHOLD || 1000);
    }

    /**
     * span开始时调用
     */
    onStart(span: ReadableSpan, parentContext: Context): void {
        // 在span开始时不需要特殊处理
    }

    /**
     * span结束时调用 - 这里是核心逻辑
     */
    onEnd(span: ReadableSpan): void {
        try {
            // 检查是否为HTTP或远程调用
            if (!this.isHttpOrRemoteCall(span)) {
                return;
            }

            const traceId = span.spanContext().traceId;
            const duration = this.getSpanDuration(span);
            const hasError = this.hasSpanError(span);

            // 检查是否为慢调用
            const isSlowCall = duration >= this.slowCallThreshold;

            // 如果是慢调用或有异常，标记整个trace需要强制采样
            if (isSlowCall || hasError) {
                this.sampler.markTraceForForcedSampling(traceId);

                // 记录日志用于调试
                if (process.env.TRACER_DEBUG === 'Y') {
                    console.log(`[EnhancedSpanProcessor] Marking trace ${traceId} for forced sampling:`, {
                        spanName: span.name,
                        duration: `${duration}ms`,
                        isSlowCall,
                        hasError,
                        spanKind: span.kind,
                        attributes: span.attributes
                    });
                }
            }
        } catch (error) {
            // 处理器不应该影响正常的tracing流程
            console.error('[EnhancedSpanProcessor] Error in onEnd:', error);
        }
    }

    /**
     * 检查是否为HTTP或远程调用相关的span
     */
    private isHttpOrRemoteCall(span: ReadableSpan): boolean {
        const spanName = span.name;
        const spanKind = span.kind;
        const attributes = span.attributes;

        // 检查span类型
        if (spanKind === SpanKind.CLIENT || spanKind === SpanKind.SERVER) {
            return true;
        }

        // 检查span名称
        if (spanName.includes('HTTP') || spanName.includes('http') ||
            spanName.includes('GET') || spanName.includes('POST') ||
            spanName.includes('PUT') || spanName.includes('DELETE') ||
            spanName.includes('PATCH')) {
            return true;
        }

        // 检查属性
        if (attributes) {
            return !!(attributes['http.method'] ||
                attributes['http.url'] ||
                attributes['http.target'] ||
                attributes['rpc.service'] ||
                attributes['db.operation'] ||
                attributes['db.statement']);
        }

        return false;
    }

    /**
     * 获取span执行时间（毫秒）
     */
    private getSpanDuration(span: ReadableSpan): number {
        const startTime = span.startTime;
        const endTime = span.endTime;

        // OpenTelemetry 使用 HrTime 格式: [seconds, nanoseconds]
        if (Array.isArray(startTime) && Array.isArray(endTime)) {
            // [seconds, nanoseconds] 格式
            const startMs = startTime[0] * 1000 + startTime[1] / 1000000;
            const endMs = endTime[0] * 1000 + endTime[1] / 1000000;
            return endMs - startMs;
        } else {
            // 兼容其他可能的格式
            const startMs = Array.isArray(startTime) ?
                startTime[0] * 1000 + startTime[1] / 1000000 :
                (startTime as unknown as number);
            const endMs = Array.isArray(endTime) ?
                endTime[0] * 1000 + endTime[1] / 1000000 :
                (endTime as unknown as number);
            return endMs - startMs;
        }
    }

    /**
     * 检查span是否有错误
     */
    private hasSpanError(span: ReadableSpan): boolean {
        const status = span.status;

        // 检查span状态
        if (status && status.code === SpanStatusCode.ERROR) {
            return true;
        }

        // 检查HTTP状态码
        const httpStatusCode = span.attributes['http.status_code'];
        if (httpStatusCode && typeof httpStatusCode === 'number') {
            // HTTP 4xx 和 5xx 状态码视为错误
            return httpStatusCode >= 400;
        }

        // 检查是否有异常事件
        const events = span.events || [];
        return events.some(event => event.name === 'exception');
    }

    /**
     * 强制刷新（同步处理）
     */
    forceFlush(): Promise<void> {
        return Promise.resolve();
    }

    /**
     * 关闭处理器
     */
    shutdown(): Promise<void> {
        return Promise.resolve();
    }
}
