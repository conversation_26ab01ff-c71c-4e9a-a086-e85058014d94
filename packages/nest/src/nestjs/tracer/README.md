# 增强链路采样器

## 概述

增强链路采样器提供了基于性能和异常的智能采样功能，确保重要的链路信息不会丢失，同时控制整体的采样开销。

## 功能特性

### 1. 智能采样策略

- **慢调用强制采样**：超过阈值的HTTP调用或远程调用的整体链路会被强制采样
- **异常调用强制采样**：产生异常的HTTP调用或远程调用的整体链路会被强制采样
- **正常采样率**：其他请求使用配置的采样率进行采样

### 2. 过滤规则

- 自动过滤健康检查请求 (`/health`)
- 过滤中间件和内部操作
- 保持与原有过滤逻辑的兼容性

### 3. 内存管理

- 自动清理过期的trace ID，防止内存泄漏
- 限制强制采样trace的数量

## 环境变量配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `TRACER_SAMPLER_RATIO` | `0.1` | 正常采样率 (0.0-1.0) |
| `TRACER_SLOW_CALL_THRESHOLD` | `1000` | 慢调用阈值 (毫秒) |
| `TRACER_DEBUG` | `N` | 是否开启调试日志 (Y/N) |
| `DISABLE_TRACER_REPORT` | `N` | 是否禁用链路追踪 (Y/N) |
| `TRACER_SERVICE_NAME` | - | 服务名称 (必需) |
| `TRACER_INTERNAL_REPORT` | `N` | 是否使用内网上报 (Y/N) |
| `HTTP_TRACES_ENDPOINT` | - | 链路追踪端点 (外网) |
| `HTTP_INTERNAL_TRACES_ENDPOINT` | - | 链路追踪端点 (内网) |

## 使用方法

### 1. 基本配置

增强采样器已经集成到现有的 OpenTelemetry 配置中，无需额外配置即可使用：

```typescript
import { Tracer } from '@yqz/nest';

// 启动链路追踪
await Tracer.start();
```

### 2. 环境变量示例

```bash
# 基础配置
TRACER_SERVICE_NAME=my-service
HTTP_TRACES_ENDPOINT=http://jaeger:14268/api/traces

# 采样配置
TRACER_SAMPLER_RATIO=0.1          # 10% 正常采样率
TRACER_SLOW_CALL_THRESHOLD=1500   # 1.5秒慢调用阈值

# 调试配置
TRACER_DEBUG=Y                    # 开启调试日志
```

### 3. 代码示例

```typescript
import { trace } from '@opentelemetry/api';

// 创建自定义span
const tracer = trace.getTracer('my-service');

await tracer.startActiveSpan('custom-operation', async (span) => {
  try {
    // 模拟慢调用 (超过阈值)
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 这个trace会被自动标记为强制采样
    span.setStatus({ code: SpanStatusCode.OK });
  } catch (error) {
    // 异常也会触发强制采样
    span.setStatus({ 
      code: SpanStatusCode.ERROR, 
      message: error.message 
    });
    span.recordException(error);
    throw error;
  } finally {
    span.end();
  }
});
```

## 工作原理

### 1. 采样决策流程

```
请求到达
    ↓
过滤规则检查 (健康检查、中间件等)
    ↓
检查是否已标记强制采样
    ↓
应用正常采样率
    ↓
创建span
    ↓
SpanProcessor监听span结束
    ↓
检测慢调用/异常
    ↓
标记trace强制采样 (影响后续span)
```

### 2. 强制采样触发条件

- **慢调用检测**：
  - HTTP客户端调用 (SpanKind.CLIENT)
  - HTTP服务端调用 (SpanKind.SERVER)
  - 包含HTTP相关属性的span
  - 执行时间超过配置阈值

- **异常检测**：
  - Span状态为ERROR
  - HTTP状态码 >= 400
  - 包含exception事件的span

### 3. 内存管理

- 维护强制采样trace ID的Set
- 当Set大小超过10000时，清理最早的5000个ID
- 避免长时间运行导致的内存泄漏

## 监控和调试

### 1. 调试日志

设置 `TRACER_DEBUG=Y` 可以看到详细的采样决策日志：

```
[EnhancedSpanProcessor] Marking trace abc123 for forced sampling: {
  spanName: "GET /api/slow-endpoint",
  duration: "1500ms",
  isSlowCall: true,
  hasError: false,
  spanKind: 3,
  attributes: { "http.method": "GET", "http.url": "/api/slow-endpoint" }
}
```

### 2. 配置验证

启动时会打印当前配置：

```
[Tracing] 增强采样器配置: {
  samplerRatio: "0.1",
  slowCallThreshold: "1000ms",
  debug: "Y"
}
```

## 测试

运行测试用例验证功能：

```bash
npm test -- enhanced-sampler.test.ts
```

测试覆盖：
- 基础过滤功能
- 强制采样机制
- 慢调用检测
- 异常检测
- 内存管理

## 注意事项

1. **性能影响**：SpanProcessor会在每个span结束时执行检查，对性能有轻微影响
2. **内存使用**：强制采样的trace ID会暂时保存在内存中
3. **采样延迟**：强制采样决策在span结束后生效，影响同一trace的后续span
4. **兼容性**：完全兼容现有的OpenTelemetry配置和使用方式

## 升级指南

从原有的CustomSampler升级到EnhancedSampler：

1. 无需修改代码，EnhancedSampler向后兼容
2. 可选：添加新的环境变量配置
3. 可选：开启调试日志验证功能

原有的所有功能和配置保持不变。
