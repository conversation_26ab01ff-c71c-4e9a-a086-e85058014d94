export type vNode = {
  type: 'text' | 'at';
  content: string; //'@elicxh' \u200C\u200C@elicxh\u200C\u200C
  match: string | number;
  info?: PersonInfo;
};

export type PersonInfo = {
  personId: string | number;
  nickName: string | number;
};
/** 循环正则匹配， hook里面处理所有的情况 */
function loopRegexp(
  str: string,
  regex: RegExp,
  hook: (isRegex: boolean, content: string, match: string) => void
) {
  const regexCpy = new RegExp(regex);
  let match: RegExpExecArray | null;
  let lastIndex = 0;
  const length = str.length;
  // content 匹配内容 origin 原始匹配内容
  while ((match = regex.exec(str)) !== null && lastIndex < length) {
    const beforeText = str.slice(lastIndex, match.index);
    if (beforeText) hook(false, beforeText, '');
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    const matches = [...match[0].matchAll(regexCpy)];
    const name = (matches[0] || [])[1];

    hook(true, match[0], name);

    lastIndex = regex.lastIndex; // 更新上一个匹配的结束位置
  }
  if (lastIndex < str.length) {
    const rest = str.slice(lastIndex, length);
    hook(false, rest, '');
  }
}
// 成功
function transferBackEnd(str: string, arr: PersonInfo[]): { nodes: vNode[] } {
  const regex = /\$(\d+)\$/g;
  const result: any[] = [];
  let index = 0;
  const dealMatch = (isRegex: boolean, content: string, match: string) => {
    const { nickName, personId } = arr[index] || {};
    // 如果匹配，且personId对应,放入数组
    if (isRegex && personId == match) {
      const nodeAt = {
        type: 'at',
        content: '\u200C\u200C@' + nickName + '\u200C\u200C',
        match: nickName,
        info: arr[index],
      };
      result.push(nodeAt);
      index++;
    } else {
      const nodeText = {
        type: 'text',
        content,
        match,
      };
      result.push(nodeText);
    }
  };
  loopRegexp(str, regex, dealMatch);
  return { nodes: result };
}

function transferInput(
  str: string,
  arr: PersonInfo[]
): { arr: PersonInfo[]; nodes: vNode[] } {
  const regex = /\u200C\u200C@([^\u200C]+)\u200C\u200C/g;
  const result: vNode[] = [];
  const arrNew: PersonInfo[] = [];
  const map = new Map();
  arr.forEach((info) => map.set(info.nickName, info.personId));
  let index = 0;
  //test
  const dealMatch = (isRegex: boolean, content: string, match: string) => {
    const { nickName } = arr[index] || {};
    //如果匹配，且能够在arr里面找到person,生成一个at节点，不然生成一个text节点
    if (isRegex && (nickName == match || !!map.get(match))) {
      const nodeAt: vNode = {
        type: 'at',
        content: String(content),
        match: match,
        info: arr[index],
      };
      result.push(nodeAt);
      arrNew.push(arr[index]);
      index++;
    } else {
      const preNode = result[result.length - 1] || {};
      const isPrevText = preNode.type === 'text';
      if (isPrevText) {
        const nodeText: vNode = {
          type: 'text',
          content: preNode.content + content.replace(/\u200C/g, '\u200B'),
          match: preNode.match + match,
        };
        result[result.length - 1] = nodeText;
      } else {
        const nodeText: vNode = {
          type: 'text',
          content: content.replace(/\u200C/g, '\u200B'),
          match,
        };
        result.push(nodeText);
      }
    }
  };
  loopRegexp(str, regex, dealMatch);
  return {
    arr: arrNew,
    nodes: result,
  };
}

function transferSubmit(vNodes: vNode[]): {
  str: string;
  arr: PersonInfo[];
} {
  let str = '';
  const arrNew: PersonInfo[] = [];

  for (let i = 0; i < vNodes.length; i++) {
    const el = vNodes[i] as { info?: PersonInfo } & vNode;

    if (el.type === 'text') {
      str += el.content;
      continue;
    }
    if (el.type === 'at') {
      if (!el.info?.personId) {
        str += el.content;
        continue;
      } else {
        str += `$${el.info?.personId}$`;
        arrNew.push(el.info);
      }
    }
  }

  // '\u200C' '\u200B' 等宽不显示
  return {
    str: str.replace(/\u200B/g, '').replace(/\u200C/g, ''),
    arr: arrNew,
  };
}

function triggerOnAt(
  vNodes: vNode[],
  position: number,
  personInfo: PersonInfo
) {
  let curPosition = 0;
  const nodes: vNode[] = [];
  const arrNew: PersonInfo[] = [];
  let curLeft = 0; // record operate node length
  if (vNodes.length == 0) {
    nodes.push({
      type: 'at',
      content: '\u200C\u200C@' + personInfo.nickName + '\u200C\u200C',
      match: personInfo.nickName,
      info: personInfo,
    });
    nodes.push({
      type: 'text',
      content: ' ',
      match: ' ',
    });
    arrNew.push(personInfo);
  }
  for (let i = 0; i < vNodes.length; i++) {
    const node = vNodes[i];
    const start = curLeft;
    const end = start + node.content.length;
    if (position < start || position > end) {
      nodes.push(node);
      if (node.type === 'at' && node.info) arrNew.push(node.info);
      curLeft += node.content.length;
    } else {
      // deal input on nodes;
      const beforeNodeContent = node.content
        .slice(0, position - start)
        .replace(/\u200C/g, '\u200B');
      const afterNodeContent = node.content
        .slice(position - start, node.content.length)
        .replace(/\u200C/g, '\u200B');
      if (beforeNodeContent) {
        nodes.push({
          type: 'text',
          content: beforeNodeContent,
          match: '',
        });
        curLeft += beforeNodeContent.length;
      }
      nodes.push({
        type: 'at',
        content: '\u200C\u200C@' + personInfo.nickName + '\u200C\u200C',
        match: personInfo.nickName,
        info: personInfo,
      });
      nodes.push({
        type: 'text',
        content: ' ',
        match: ' ',
      });
      arrNew.push(personInfo);
      curLeft += node.content.length;
      curPosition = curLeft;
      if (afterNodeContent) {
        nodes.push({
          type: 'text',
          content: afterNodeContent,
          match: '',
        });
        curLeft += afterNodeContent.length;
      }
    }
  }
  return {
    nodes,
    arr: arrNew,
    curPosition,
  };
}

export { transferBackEnd, transferInput, triggerOnAt, transferSubmit };
