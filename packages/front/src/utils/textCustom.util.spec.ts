import {
  PersonInfo,
  transferBackEnd,
  transferInput,
  triggerOnAt,
  transferSubmit,
  vNode,
} from './textCustom.util';

describe('textCustom.util', () => {
  describe('transferInput', () => {
    it('识别单个@,前后无文本', () => {
      const str = '\u200C\u200C@电信集团@张八\u200C\u200C'
      const arr: PersonInfo[] = [
        { personId: 12031, nickName: '电信集团@张八' },
      ];

      const result = transferInput(str, arr);

      expect(result.arr).toHaveLength(1);
      expect(result.nodes).toHaveLength(1);
      expect(result.nodes[0].type).toBe('at');
    });
    it('识别单个@,前后有文本', () => {
      const str = 'testt\u200C\u200C@电信集团@张八\u200C\u200Chello';
      const arr: PersonInfo[] = [
        { personId: 12031, nickName: '电信集团@张八' },
      ];

      const result = transferInput(str, arr);

      expect(result.arr).toHaveLength(1);
      expect(result.nodes).toHaveLength(3);
      expect(result.nodes[0].type).toBe('text');
      expect(result.nodes[0].content).toBe('testt');
      expect(result.nodes[1].type).toBe('at');
      expect(result.nodes[1].content).toBe('\u200C\u200C@电信集团@张八\u200C\u200C');
      expect(result.nodes[2].type).toBe('text');
      expect(result.nodes[2].content).toBe('hello');
    });
    it('@之后删除昵称中的一个字', () => {
      const str = '\u200C\u200C@电信团@张八\u200C\u200C';
      const arr: PersonInfo[] = [
        { personId: 12031, nickName: '电信集团@张八' },
      ];

      const result = transferInput(str, arr);
      // console.log('result', result);
      expect(result.arr).toHaveLength(0);
      expect(result.nodes).toHaveLength(1);
      expect(result.nodes[0].type).toBe('text');
    });
  });
});
