# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [0.6.3](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.6.2...@yqz/common@0.6.3) (2022-11-26)

**Note:** Version bump only for package @yqz/common

## [0.6.2](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.6.1...@yqz/common@0.6.2) (2022-11-26)

**Note:** Version bump only for package @yqz/common

## [0.6.1](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.6.0...@yqz/common@0.6.1) (2022-11-26)

**Note:** Version bump only for package @yqz/common

# [0.6.0](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.5.0...@yqz/common@0.6.0) (2022-11-26)

### Features

- add interceptorts ([420480e](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/commits/420480e9d63b5a07e91d3fd243d5df8f6cf1ed2f))

# [0.5.0](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.4.1...@yqz/common@0.5.0) (2022-11-26)

### Features

- rabbmitmq version 3.3.0 ([3046538](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/commits/304653839ef30aa1ec25a24cf9a3ec1df08f60dd))

## [0.4.1](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.4.0...@yqz/common@0.4.1) (2022-11-26)

**Note:** Version bump only for package @yqz/common

# [0.4.0](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.3.2...@yqz/common@0.4.0) (2022-11-26)

### Features

- upgrade redis ([2615a87](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/commits/2615a870086d1ccfa2467a6b3b4d8e22730c444c))

## [0.3.2](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.3.1...@yqz/common@0.3.2) (2022-11-26)

**Note:** Version bump only for package @yqz/common

## [0.3.1](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.3.0...@yqz/common@0.3.1) (2022-11-25)

### Bug Fixes

- 忘了 export ([7457189](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/commits/74571899e2a71e75f0ff5dcbc1387b7b5b0548ee))

# [0.3.0](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.2.1...@yqz/common@0.3.0) (2022-11-25)

### Features

- 新建各种 decorators ([e42d08c](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/commits/e42d08c1a73e115d6d8ea20e698fe06a100c93f8))

## [0.2.1](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.2.0...@yqz/common@0.2.1) (2022-11-25)

**Note:** Version bump only for package @yqz/common

# [0.2.0](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.1.0...@yqz/common@0.2.0) (2022-11-25)

### Features

- ImApi ([19ff53a](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/commits/19ff53a80379ee11c83f1b80dd0fc6ad094d2213))

# [0.1.0](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.0.9...@yqz/common@0.1.0) (2022-11-25)

### Features

- my-meter ([98d123e](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/commits/98d123e43e0b520c4588a2034b4b98745bdac87f))

## [0.0.9](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.0.7-rc.1...@yqz/common@0.0.9) (2022-11-24)

**Note:** Version bump only for package @yqz/common

## [0.0.7-rc.1](https://codeup.aliyun.com/1qizhuang/coding/npm/yqz-packages/compare/@yqz/common@0.0.7-rc.0...@yqz/common@0.0.7-rc.1) (2022-11-24)

**Note:** Version bump only for package @yqz/common

## 0.0.7-rc.0 (2022-11-24)

**Note:** Version bump only for package @yqz/common
