{"name": "@yqz/front", "version": "0.0.18", "description": "Common module and utility building blocks for badass NestJS applications", "license": "MIT", "keywords": ["NestJS"], "main": "lib/index.js", "typings": "lib/index.d.ts", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "repository": {"type": "git"}, "scripts": {"build": "tsc --build tsconfig.build.json", "build:watch": "tsc --build tsconfig.build.json --watch", "dev": "tsc-watch -p tsconfig.build.json --onSuccess \"nodemon\"", "test": "jest"}, "publishConfig": {"access": "public"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.ts$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}, "gitHead": "4d9b7fb54f6b0b3d8ea9ad2399801c421ee8aa7b"}