{"name": "@yqz/base", "version": "0.0.21", "description": "Common module and utility building blocks for badass NestJS applications", "license": "MIT", "keywords": ["NestJS"], "main": "lib/index.js", "typings": "lib/index.d.ts", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "repository": {"type": "git"}, "scripts": {"build": "tsc --build tsconfig.build.json", "build:watch": "tsc --build tsconfig.build.json --watch", "dev": "tsc-watch -p tsconfig.build.json --onSuccess \"nodemon\"", "test": "jest"}, "publishConfig": {"access": "public"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.ts$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}, "gitHead": "f59e44da4bc440f4d4082968937aa85bcbcc8b1c", "dependencies": {"md5": "^2.3.0"}}