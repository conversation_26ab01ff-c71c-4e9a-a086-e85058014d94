import * as md5 from 'md5';
export { signParams, verifyParams };

/**
 * 修改params,增加一个sign属性,用于安全性校验
 * 同时会在params中增加一个t属性(时间戳),如果原来就有就不添加
 * @param params
 * @param secret
 * @returns
 */
function signParams(params: { [key: string]: any }, secret: string) {
  try {
    if (params.sign) return params;
    if (!secret) return params;
    params.t = String(params.t || new Date().getTime());
    const ascending = Number(params.t.slice(-1)) % 2 === 0;

    const sortedKeys = ascending
      ? Object.keys(params).sort()
      : Object.keys(params).sort().reverse();

    // remove undefined or null values
    const filteredKeys = sortedKeys.filter((key) => shouldKeep(params[key]));
    const paramsStr = filteredKeys.map((key) => key + params[key]).join('');
    // console.log(params, sortedKeys, filteredKeys)
    params.sign = md5(secret + paramsStr);
    return params;
  } catch (e) {
    console.error(`[signUrl] error`, (e as any).message);
    return params;
  }
}

/**
 * 验证params里的sign是否正确, 必须放在拦截器的最后(所有对params的修改操作已经结束)
 * @param params.options.maxToleranceInSeconds 时间戳参数t允许的最大误差(单位秒)
 * @param key
 */
function verifyParams(
  params: { [key: string]: any },
  secret: string,
  options: { maxToleranceInSeconds: number } = { maxToleranceInSeconds: 60 }
): { valid: boolean; msg?: string } {
  if (!secret) throw new Error('[verifyParams] secret is not provided');
  if (!params.sign) return { valid: false, msg: 'no sign' };

  if (!params.t) return { valid: false, msg: 'missing t' };
  if (
    Math.abs(new Date().getTime() - Number(params.t)) >
    options.maxToleranceInSeconds * 1000
  )
    return { valid: false, msg: 'invalid t' };

  //复制一份,避免修改传入的params
  const myParams = Object.assign({}, params);
  delete myParams.sign;
  signParams(myParams, secret);
  const valid = myParams.sign === params.sign;
  if (!valid) console.log('origin', params.sign, 'verifiy', myParams.sign);
  return {
    valid,
    msg: valid ? '' : 'invalid sign',
  };
}

function shouldKeep(value: any) {
  if (typeof value === 'string' && value.trim() === '') return false;
  if (value === true || value === 'true') return false;
  if (value === false || value === 'false') return false;
  if (
    typeof value === 'number' &&
    (Number.isNaN(value) || !Number.isFinite(value))
  )
    return false;
  if (value === null || value === 'null') return false;
  if (value === undefined || value === 'undefined') return false;
  return true;
}
