#!/usr/bin/env node
import * as colors from 'colors';
import * as _ from 'lodash';
import * as request from 'request';
import * as shell from 'shelljs';
import { ServiceApiMap } from './config';
import * as fs from 'fs';

if (process.argv.length != 3) {
  console.log(
    colors.red(
      '需要指定目标环境: \n开发环境: npx make-env dev\n生产环境: npx make-env prd'
    )
  );
  process.exit(1);
}

const targetEnv = process.argv[2];

const res = shell.exec(
  `grep -r --exclude-dir=dist --exclude-dir=node_modules 'process.env' ./`,
  { silent: true }
).stdout;
const lines = res.split('\n');
const pattern = /process\.env\.[\w\-\_]+/;
const mathes = lines.map((line) => {
  const match = pattern.exec(line);
  if (match) return match[0];
});
const filtered = _.orderBy(_.filter(mathes, (e) => !!e));
const usedEnvList = _.uniq(filtered).map((e) => e.split('process.env.')[1]);
// if (!usedEnvList.includes('YQZ_TARGET_ENV')) {
//   console.error(
//     colors.red('项目中没有找到process.env.YQZ_TARGET_ENV, 这是必须的!')
//   );
//   process.exit(1);
// }

const serviceName = JSON.parse(fs.readFileSync('package.json').toString()).name;
// const serviceName = 'smart-ops';
const envApi = `https://api${
  targetEnv === 'dev' ? '-dev' : ''
}.1qizhuang.com/api/${serviceName}/env`;
console.log(colors.blue(`requsting ${envApi}`));

request({ url: envApi, timeout: 3000 }, (err, res) => {
  if (err) {
    console.error(err.message);
    process.exit(1);
  }

  const onlineEnvMap = JSON.parse(res.body);
  const resEnvList: string[] = [];
  _.keys(onlineEnvMap).forEach((env) => {
    // 优先读取ServiceApiMap进行替换
    const value = (ServiceApiMap[targetEnv][env] || onlineEnvMap[env] || '')
      .replace(/y67b85exaplgtbmhofn0-rw4rm/g, 'yqz')
      .replace(/web-internal-dev/g, 'cdn-dev')
      .replace(/web-internal-prd/g, 'cdn')
      .replace(/-internal/g, '');
    const entry = [env, value].join('=');
    const yqz_nest_env = ['YQZ_REDIS_PWD'];
    if (
      value.includes('***') &&
      !usedEnvList.includes(env) &&
      !yqz_nest_env.includes(env)
    )
      return;

    resEnvList.push(entry);
  });

  console.log(
    [
      ..._.sortBy(resEnvList),
      '# 本地开发:默认关闭mq, cron等',
      'ALLOW_DEV_TIMOUT=true',
      'YQZ_DISABLE_MY_TASK=Y',
      'YQZ_MQ_REGISTER_HANDLER=false',
    ].join('\n')
  );
});
